-- <PERSON><PERSON>t untuk memperbaiki dan menambahkan kolom yang diperlukan
USE db_ecommerce;

-- Tambahkan kolom payment_method ke tabel orders jika belum ada
ALTER TABLE orders ADD COLUMN IF NOT EXISTS payment_method VARCHAR(50);

-- Tambahkan kolom subtotal ke tabel order_details untuk kemudahan perhitungan
ALTER TABLE order_details ADD COLUMN IF NOT EXISTS subtotal DECIMAL(12,2);

-- Update subtotal untuk data yang sudah ada (jika ada)
UPDATE order_details SET subtotal = price * quantity WHERE subtotal IS NULL;

-- Pastikan kolom image_url sudah ada di tabel products (sudah ada di struktur Anda)
-- ALTER TABLE products ADD COLUMN IF NOT EXISTS image_url VARCHAR(255);

-- Tambahkan index untuk performa yang lebih baik
CREATE INDEX IF NOT EXISTS idx_orders_user_id ON orders(user_id);
CREATE INDEX IF NOT EXISTS idx_orders_status ON orders(order_status);
CREATE INDEX IF NOT EXISTS idx_orders_date ON orders(order_date);
CREATE INDEX IF NOT EXISTS idx_order_details_order_id ON order_details(order_id);
CREATE INDEX IF NOT EXISTS idx_order_details_product_id ON order_details(product_id);
CREATE INDEX IF NOT EXISTS idx_products_category ON products(category_id);
CREATE INDEX IF NOT EXISTS idx_wishlist_user ON wishlist(user_id);

-- Tampilkan struktur tabel yang sudah diperbaiki
DESCRIBE orders;
DESCRIBE order_details;
DESCRIBE products;
