﻿Imports MySql.Data.MySqlClient

Module ModulKoneksi
    Public conn As MySqlConnection
    Public cmd As MySqlCommand
    Public da As MySqlDataAdapter
    Public dr As MySqlDataReader
    Public ds As DataSet
    Public str As String

    Sub koneksi()
        Try
            str = "Server=127.0.0.1;User Id=root;Password=**************;Database=db_ecommerce"
            conn = New MySqlConnection(str)
            If conn.State = ConnectionState.Closed Then
                conn.Open()
            End If
        Catch ex As Exception
            MessageBox.Show("Koneksi gagal: " & ex.Message, "Error", MessageBoxButtons.OK, MessageBoxIcon.Error)
        End Try
    End Sub

    Sub tutupKoneksi()
        If conn.State = ConnectionState.Open Then
            conn.Close()
        End If
    End Sub
End Module