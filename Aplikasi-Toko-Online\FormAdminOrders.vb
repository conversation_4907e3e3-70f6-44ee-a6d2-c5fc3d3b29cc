' FormAdminOrders.vb - Form khusus admin untuk mengelola pesanan
Imports MySql.Data.MySqlClient

Public Class FormAdminOrders
    Private Sub FormAdminOrders_Load(sender As Object, e As EventArgs) Handles MyBase.Load
        Me.Text = "Admin - Ke<PERSON>la <PERSON>"
        Me.Size = New Size(1300, 750)
        Me.StartPosition = FormStartPosition.CenterScreen
        Me.BackColor = Color.FromArgb(248, 249, 250)

        CreateUI()
        LoadOrders()
    End Sub

    Private Sub CreateUI()
        ' Header Panel
        Dim pnlHeader As New Panel With {
            .Dock = DockStyle.Top,
            .Height = 80,
            .BackColor = Color.FromArgb(52, 58, 64)
        }

        Dim lblTitle As New Label With {
            .Text = "KELOLA PESANAN",
            .Font = New Font("Segoe UI", 20, FontStyle.Bold),
            .ForeColor = Color.White,
            .Location = New Point(20, 25),
            .AutoSize = True
        }

        Dim btnClose As New Button With {
            .Text = "✕",
            .Size = New Size(40, 40),
            .Location = New Point(Me.Width - 60, 20),
            .FlatStyle = FlatStyle.Flat,
            .BackColor = Color.FromArgb(220, 53, 69),
            .ForeColor = Color.White,
            .Font = New Font("Segoe UI", 16, FontStyle.Bold),
            .Cursor = Cursors.Hand
        }
        btnClose.FlatAppearance.BorderSize = 0
        AddHandler btnClose.Click, Sub() Me.Close()

        pnlHeader.Controls.AddRange({lblTitle, btnClose})

        ' Toolbar Panel
        Dim pnlToolbar As New Panel With {
            .Dock = DockStyle.Top,
            .Height = 60,
            .BackColor = Color.White,
            .Padding = New Padding(20, 10, 20, 10)
        }

        ' Search controls
        Dim lblSearch As New Label With {
            .Text = "Cari Order ID:",
            .Font = New Font("Segoe UI", 10),
            .Location = New Point(20, 18),
            .AutoSize = True
        }

        Dim txtSearch As New TextBox With {
            .Name = "txtSearch",
            .Size = New Size(150, 25),
            .Location = New Point(120, 15),
            .Font = New Font("Segoe UI", 10)
        }
        AddHandler txtSearch.TextChanged, AddressOf TxtSearch_TextChanged

        ' Status filter
        Dim lblStatus As New Label With {
            .Text = "Status:",
            .Font = New Font("Segoe UI", 10),
            .Location = New Point(290, 18),
            .AutoSize = True
        }

        Dim cmbStatus As New ComboBox With {
            .Name = "cmbStatus",
            .Size = New Size(120, 25),
            .Location = New Point(340, 15),
            .DropDownStyle = ComboBoxStyle.DropDownList,
            .Font = New Font("Segoe UI", 10)
        }
        cmbStatus.Items.AddRange({"Semua Status", "pending", "processing", "shipped", "delivered", "cancelled"})
        cmbStatus.SelectedIndex = 0
        AddHandler cmbStatus.SelectedIndexChanged, AddressOf CmbStatus_SelectedIndexChanged

        ' Action buttons
        Dim btnUpdateStatus As New Button With {
            .Text = "📝 UPDATE STATUS",
            .Size = New Size(140, 35),
            .Location = New Point(480, 12),
            .FlatStyle = FlatStyle.Flat,
            .BackColor = Color.FromArgb(255, 193, 7),
            .ForeColor = Color.White,
            .Font = New Font("Segoe UI", 10, FontStyle.Bold),
            .Cursor = Cursors.Hand
        }
        btnUpdateStatus.FlatAppearance.BorderSize = 0
        AddHandler btnUpdateStatus.Click, AddressOf BtnUpdateStatus_Click

        Dim btnViewDetail As New Button With {
            .Text = "👁️ DETAIL",
            .Size = New Size(100, 35),
            .Location = New Point(630, 12),
            .FlatStyle = FlatStyle.Flat,
            .BackColor = Color.FromArgb(52, 152, 219),
            .ForeColor = Color.White,
            .Font = New Font("Segoe UI", 10, FontStyle.Bold),
            .Cursor = Cursors.Hand
        }
        btnViewDetail.FlatAppearance.BorderSize = 0
        AddHandler btnViewDetail.Click, AddressOf BtnViewDetail_Click

        Dim btnRefresh As New Button With {
            .Text = "🔄 REFRESH",
            .Size = New Size(100, 35),
            .Location = New Point(740, 12),
            .FlatStyle = FlatStyle.Flat,
            .BackColor = Color.FromArgb(108, 117, 125),
            .ForeColor = Color.White,
            .Font = New Font("Segoe UI", 10, FontStyle.Bold),
            .Cursor = Cursors.Hand
        }
        btnRefresh.FlatAppearance.BorderSize = 0
        AddHandler btnRefresh.Click, Sub() LoadOrders()

        pnlToolbar.Controls.AddRange({lblSearch, txtSearch, lblStatus, cmbStatus, btnUpdateStatus, btnViewDetail, btnRefresh})

        ' Main Panel for DataGridView
        Dim pnlMain As New Panel With {
            .Dock = DockStyle.Fill,
            .Padding = New Padding(20)
        }

        ' DataGridView
        Dim dgvOrders As New DataGridView With {
            .Name = "dgvOrders",
            .Dock = DockStyle.Fill,
            .AutoSizeColumnsMode = DataGridViewAutoSizeColumnsMode.Fill,
            .AllowUserToAddRows = False,
            .AllowUserToDeleteRows = False,
            .SelectionMode = DataGridViewSelectionMode.FullRowSelect,
            .ReadOnly = True,
            .BackgroundColor = Color.White,
            .BorderStyle = BorderStyle.None,
            .RowHeadersVisible = False,
            .Font = New Font("Segoe UI", 10),
            .RowTemplate = New DataGridViewRow With {.Height = 45}
        }

        ' Style the DataGridView
        dgvOrders.DefaultCellStyle.SelectionBackColor = Color.FromArgb(52, 152, 219)
        dgvOrders.DefaultCellStyle.SelectionForeColor = Color.White
        dgvOrders.DefaultCellStyle.Padding = New Padding(5)
        dgvOrders.ColumnHeadersDefaultCellStyle.BackColor = Color.FromArgb(52, 58, 64)
        dgvOrders.ColumnHeadersDefaultCellStyle.ForeColor = Color.White
        dgvOrders.ColumnHeadersDefaultCellStyle.Font = New Font("Segoe UI", 11, FontStyle.Bold)
        dgvOrders.ColumnHeadersHeight = 45
        dgvOrders.EnableHeadersVisualStyles = False

        AddHandler dgvOrders.CellDoubleClick, AddressOf DgvOrders_CellDoubleClick

        pnlMain.Controls.Add(dgvOrders)

        ' Status Panel
        Dim pnlStatus As New Panel With {
            .Dock = DockStyle.Bottom,
            .Height = 30,
            .BackColor = Color.FromArgb(248, 249, 250)
        }

        Dim lblStatusInfo As New Label With {
            .Name = "lblStatusInfo",
            .Text = "Siap",
            .Font = New Font("Segoe UI", 9),
            .ForeColor = Color.FromArgb(108, 117, 125),
            .Location = New Point(20, 8),
            .AutoSize = True
        }

        pnlStatus.Controls.Add(lblStatusInfo)

        Me.Controls.AddRange({pnlMain, pnlStatus, pnlToolbar, pnlHeader})
    End Sub

    Private Sub LoadOrders(Optional searchText As String = "", Optional statusFilter As String = "")
        Try
            koneksi()

            Dim query As String = "SELECT o.order_id as 'Order ID', u.username as 'Customer', " &
                                 "o.order_date as 'Tanggal Order', o.total_amount as 'Total', " &
                                 "o.order_status as 'Status', o.shipping_address as 'Alamat Pengiriman' " &
                                 "FROM orders o JOIN users u ON o.user_id = u.user_id WHERE 1=1 "

            If searchText <> "" Then
                query &= "AND o.order_id LIKE '%" & searchText & "%' "
            End If

            If statusFilter <> "" AndAlso statusFilter <> "Semua Status" Then
                query &= "AND o.order_status = '" & statusFilter & "' "
            End If

            query &= "ORDER BY o.order_date DESC"

            cmd = New MySqlCommand(query, conn)
            Dim adapter As New MySqlDataAdapter(cmd)
            Dim dt As New DataTable()
            adapter.Fill(dt)

            Dim dgv As DataGridView = CType(Me.Controls.Find("dgvOrders", True).FirstOrDefault(), DataGridView)
            If dgv IsNot Nothing Then
                dgv.DataSource = dt

                ' Format columns
                If dgv.Columns.Contains("Total") Then
                    dgv.Columns("Total").DefaultCellStyle.Format = "C0"
                    dgv.Columns("Total").DefaultCellStyle.FormatProvider = New System.Globalization.CultureInfo("id-ID")
                End If

                If dgv.Columns.Contains("Tanggal Order") Then
                    dgv.Columns("Tanggal Order").DefaultCellStyle.Format = "dd/MM/yyyy HH:mm"
                End If

                ' Color code status
                For Each row As DataGridViewRow In dgv.Rows
                    If row.Cells("Status").Value IsNot Nothing Then
                        Dim status As String = row.Cells("Status").Value.ToString().ToLower()
                        Select Case status
                            Case "pending"
                                row.Cells("Status").Style.BackColor = Color.FromArgb(255, 193, 7)
                                row.Cells("Status").Style.ForeColor = Color.White
                            Case "processing"
                                row.Cells("Status").Style.BackColor = Color.FromArgb(52, 152, 219)
                                row.Cells("Status").Style.ForeColor = Color.White
                            Case "shipped"
                                row.Cells("Status").Style.BackColor = Color.FromArgb(255, 152, 0)
                                row.Cells("Status").Style.ForeColor = Color.White
                            Case "delivered"
                                row.Cells("Status").Style.BackColor = Color.FromArgb(40, 167, 69)
                                row.Cells("Status").Style.ForeColor = Color.White
                            Case "cancelled"
                                row.Cells("Status").Style.BackColor = Color.FromArgb(220, 53, 69)
                                row.Cells("Status").Style.ForeColor = Color.White
                        End Select
                    End If
                Next
            End If

            ' Update status
            Dim lblStatus As Label = CType(Me.Controls.Find("lblStatusInfo", True).FirstOrDefault(), Label)
            If lblStatus IsNot Nothing Then
                lblStatus.Text = $"Total: {dt.Rows.Count} pesanan"
            End If

            tutupKoneksi()

        Catch ex As Exception
            MessageBox.Show("Error loading orders: " & ex.Message, "Error", MessageBoxButtons.OK, MessageBoxIcon.Error)
        End Try
    End Sub

    Private Sub TxtSearch_TextChanged(sender As Object, e As EventArgs)
        Dim searchText As String = CType(sender, TextBox).Text.Trim()
        Dim cmbStatus As ComboBox = CType(Me.Controls.Find("cmbStatus", True).FirstOrDefault(), ComboBox)
        Dim statusFilter As String = If(cmbStatus IsNot Nothing, cmbStatus.Text, "")
        LoadOrders(searchText, statusFilter)
    End Sub

    Private Sub CmbStatus_SelectedIndexChanged(sender As Object, e As EventArgs)
        Dim statusFilter As String = CType(sender, ComboBox).Text
        Dim txtSearch As TextBox = CType(Me.Controls.Find("txtSearch", True).FirstOrDefault(), TextBox)
        Dim searchText As String = If(txtSearch IsNot Nothing, txtSearch.Text.Trim(), "")
        LoadOrders(searchText, statusFilter)
    End Sub

    Private Sub DgvOrders_CellDoubleClick(sender As Object, e As DataGridViewCellEventArgs)
        If e.RowIndex >= 0 Then
            BtnViewDetail_Click(Nothing, Nothing)
        End If
    End Sub

    Private Sub BtnUpdateStatus_Click(sender As Object, e As EventArgs)
        Dim dgv As DataGridView = CType(Me.Controls.Find("dgvOrders", True).FirstOrDefault(), DataGridView)

        If dgv Is Nothing OrElse dgv.SelectedRows.Count = 0 Then
            MessageBox.Show("Pilih pesanan yang akan diupdate statusnya!", "Peringatan", MessageBoxButtons.OK, MessageBoxIcon.Warning)
            Return
        End If

        Dim orderID As String = dgv.SelectedRows(0).Cells("Order ID").Value.ToString()
        Dim currentStatus As String = dgv.SelectedRows(0).Cells("Status").Value.ToString()

        ' Create status update dialog
        Dim statusForm As New Form With {
            .Text = "Update Status Pesanan",
            .Size = New Size(400, 200),
            .StartPosition = FormStartPosition.CenterParent,
            .FormBorderStyle = FormBorderStyle.FixedDialog,
            .MaximizeBox = False,
            .MinimizeBox = False
        }

        Dim lblOrder As New Label With {
            .Text = $"Order ID: {orderID}",
            .Font = New Font("Segoe UI", 12, FontStyle.Bold),
            .Location = New Point(20, 20),
            .AutoSize = True
        }

        Dim lblCurrentStatus As New Label With {
            .Text = $"Status saat ini: {currentStatus}",
            .Font = New Font("Segoe UI", 10),
            .Location = New Point(20, 50),
            .AutoSize = True
        }

        Dim lblNewStatus As New Label With {
            .Text = "Status baru:",
            .Font = New Font("Segoe UI", 10),
            .Location = New Point(20, 80),
            .AutoSize = True
        }

        Dim cmbNewStatus As New ComboBox With {
            .Size = New Size(200, 25),
            .Location = New Point(120, 77),
            .DropDownStyle = ComboBoxStyle.DropDownList,
            .Font = New Font("Segoe UI", 10)
        }
        cmbNewStatus.Items.AddRange({"pending", "processing", "shipped", "delivered", "cancelled"})
        cmbNewStatus.Text = currentStatus

        Dim btnUpdate As New Button With {
            .Text = "UPDATE",
            .Size = New Size(80, 30),
            .Location = New Point(200, 120),
            .BackColor = Color.FromArgb(40, 167, 69),
            .ForeColor = Color.White,
            .FlatStyle = FlatStyle.Flat
        }
        btnUpdate.FlatAppearance.BorderSize = 0

        Dim btnCancel As New Button With {
            .Text = "BATAL",
            .Size = New Size(80, 30),
            .Location = New Point(290, 120),
            .BackColor = Color.FromArgb(108, 117, 125),
            .ForeColor = Color.White,
            .FlatStyle = FlatStyle.Flat
        }
        btnCancel.FlatAppearance.BorderSize = 0

        AddHandler btnUpdate.Click, Sub()
                                        Try
                                            koneksi()
                                            cmd = New MySqlCommand("UPDATE orders SET order_status = @status WHERE order_id = @id", conn)
                                            cmd.Parameters.AddWithValue("@status", cmbNewStatus.Text)
                                            cmd.Parameters.AddWithValue("@id", orderID)
                                            cmd.ExecuteNonQuery()
                                            tutupKoneksi()

                                            MessageBox.Show("Status pesanan berhasil diupdate!", "Sukses", MessageBoxButtons.OK, MessageBoxIcon.Information)
                                            statusForm.Close()
                                            LoadOrders()

                                        Catch ex As Exception
                                            MessageBox.Show("Error updating status: " & ex.Message, "Error", MessageBoxButtons.OK, MessageBoxIcon.Error)
                                        End Try
                                    End Sub

        AddHandler btnCancel.Click, Sub() statusForm.Close()

        statusForm.Controls.AddRange({lblOrder, lblCurrentStatus, lblNewStatus, cmbNewStatus, btnUpdate, btnCancel})
        statusForm.ShowDialog()
    End Sub

    Private Sub BtnViewDetail_Click(sender As Object, e As EventArgs)
        Dim dgv As DataGridView = CType(Me.Controls.Find("dgvOrders", True).FirstOrDefault(), DataGridView)

        If dgv Is Nothing OrElse dgv.SelectedRows.Count = 0 Then
            MessageBox.Show("Pilih pesanan yang akan dilihat detailnya!", "Peringatan", MessageBoxButtons.OK, MessageBoxIcon.Warning)
            Return
        End If

        Dim orderID As String = dgv.SelectedRows(0).Cells("Order ID").Value.ToString()

        ' You can create a detailed order view form here
        ' For now, just show basic info
        MessageBox.Show($"Detail pesanan {orderID} akan ditampilkan di form terpisah", "Info", MessageBoxButtons.OK, MessageBoxIcon.Information)
    End Sub
End Class
