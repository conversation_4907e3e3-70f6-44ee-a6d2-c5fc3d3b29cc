Imports MySql.Data.MySqlClient
Imports System.Drawing

Public Class FormPaymentTest
    Private orderID As Integer
    Private paymentMethod As String
    Private totalAmount As Decimal
    
    Public Sub New(orderID As Integer, paymentMethod As String, totalAmount As Decimal)
        InitializeComponent()
        Me.orderID = orderID
        Me.paymentMethod = paymentMethod
        Me.totalAmount = totalAmount
    End Sub
    
    Private Sub FormPaymentTest_Load(sender As Object, e As EventArgs) Handles MyBase.Load
        Me.Text = "Test Pembayaran - " & paymentMethod
        Me.Size = New Size(600, 500)
        Me.StartPosition = FormStartPosition.CenterScreen
        Me.BackColor = Color.FromArgb(245, 245, 245)
        Me.FormBorderStyle = FormBorderStyle.FixedSingle
        Me.MaximizeBox = False
        Me.MinimizeBox = False
        
        CreatePaymentTestUI()
    End Sub
    
    Private Sub CreatePaymentTestUI()
        ' Header Panel
        Dim pnlHeader As New Panel With {
            .Location = New Point(20, 20),
            .Size = New Size(540, 80),
            .BackColor = Color.FromArgb(52, 152, 219),
            .BorderStyle = BorderStyle.None
        }
        
        Dim lblTitle As New Label With {
            .Text = "🧪 SIMULASI PEMBAYARAN",
            .Font = New Font("Segoe UI", 16, FontStyle.Bold),
            .ForeColor = Color.White,
            .Location = New Point(20, 15),
            .AutoSize = True
        }
        
        Dim lblSubtitle As New Label With {
            .Text = $"Metode: {paymentMethod} | Total: Rp {totalAmount:N0}",
            .Font = New Font("Segoe UI", 11),
            .ForeColor = Color.White,
            .Location = New Point(20, 45),
            .AutoSize = True
        }
        
        pnlHeader.Controls.AddRange({lblTitle, lblSubtitle})
        Me.Controls.Add(pnlHeader)
        
        ' Payment Method Specific UI
        Select Case paymentMethod
            Case "Transfer Bank"
                CreateTransferBankUI()
            Case "COD (Cash on Delivery)"
                CreateCODUI()
            Case "E-Wallet"
                CreateEWalletUI()
            Case "Kartu Kredit"
                CreateCreditCardUI()
        End Select
        
        ' Action Buttons
        CreateActionButtons()
    End Sub
    
    Private Sub CreateTransferBankUI()
        Dim pnlBank As New Panel With {
            .Location = New Point(20, 120),
            .Size = New Size(540, 200),
            .BackColor = Color.White,
            .BorderStyle = BorderStyle.FixedSingle
        }
        
        Dim lblBankInfo As New Label With {
            .Text = "🏦 INFORMASI TRANSFER BANK" & vbCrLf & vbCrLf &
                   "Bank: BCA" & vbCrLf &
                   "No. Rekening: **********" & vbCrLf &
                   "Atas Nama: TOKO ONLINE" & vbCrLf &
                   "Jumlah Transfer: Rp " & totalAmount.ToString("N0") & vbCrLf & vbCrLf &
                   "⚠️ Untuk testing, klik tombol 'Simulasi Bayar' di bawah",
            .Font = New Font("Segoe UI", 11),
            .Location = New Point(20, 20),
            .Size = New Size(500, 160),
            .ForeColor = Color.FromArgb(44, 62, 80)
        }
        
        pnlBank.Controls.Add(lblBankInfo)
        Me.Controls.Add(pnlBank)
    End Sub
    
    Private Sub CreateCODUI()
        Dim pnlCOD As New Panel With {
            .Location = New Point(20, 120),
            .Size = New Size(540, 200),
            .BackColor = Color.White,
            .BorderStyle = BorderStyle.FixedSingle
        }
        
        Dim lblCODInfo As New Label With {
            .Text = "💵 CASH ON DELIVERY (COD)" & vbCrLf & vbCrLf &
                   "Pembayaran dilakukan saat barang diterima" & vbCrLf &
                   "Total yang harus dibayar: Rp " & totalAmount.ToString("N0") & vbCrLf & vbCrLf &
                   "✅ Pesanan akan langsung diproses" & vbCrLf &
                   "📦 Siapkan uang pas saat barang tiba" & vbCrLf & vbCrLf &
                   "⚠️ Untuk testing, klik 'Konfirmasi COD' di bawah",
            .Font = New Font("Segoe UI", 11),
            .Location = New Point(20, 20),
            .Size = New Size(500, 160),
            .ForeColor = Color.FromArgb(44, 62, 80)
        }
        
        pnlCOD.Controls.Add(lblCODInfo)
        Me.Controls.Add(pnlCOD)
    End Sub
    
    Private Sub CreateEWalletUI()
        Dim pnlEWallet As New Panel With {
            .Location = New Point(20, 120),
            .Size = New Size(540, 200),
            .BackColor = Color.White,
            .BorderStyle = BorderStyle.FixedSingle
        }
        
        Dim lblEWalletInfo As New Label With {
            .Text = "📱 E-WALLET PAYMENT" & vbCrLf & vbCrLf &
                   "Pilihan E-Wallet: OVO, GoPay, DANA, LinkAja" & vbCrLf &
                   "Total Pembayaran: Rp " & totalAmount.ToString("N0") & vbCrLf & vbCrLf &
                   "📲 Scan QR Code atau masukkan nomor HP" & vbCrLf &
                   "⚡ Pembayaran instan" & vbCrLf & vbCrLf &
                   "⚠️ Untuk testing, klik 'Simulasi E-Wallet' di bawah",
            .Font = New Font("Segoe UI", 11),
            .Location = New Point(20, 20),
            .Size = New Size(500, 160),
            .ForeColor = Color.FromArgb(44, 62, 80)
        }
        
        pnlEWallet.Controls.Add(lblEWalletInfo)
        Me.Controls.Add(pnlEWallet)
    End Sub
    
    Private Sub CreateCreditCardUI()
        Dim pnlCard As New Panel With {
            .Location = New Point(20, 120),
            .Size = New Size(540, 200),
            .BackColor = Color.White,
            .BorderStyle = BorderStyle.FixedSingle
        }
        
        Dim lblCardInfo As New Label With {
            .Text = "💳 KARTU KREDIT" & vbCrLf & vbCrLf &
                   "Kartu yang diterima: Visa, MasterCard, JCB" & vbCrLf &
                   "Total Pembayaran: Rp " & totalAmount.ToString("N0") & vbCrLf & vbCrLf &
                   "🔒 Transaksi aman dengan enkripsi SSL" & vbCrLf &
                   "💰 Cicilan 0% tersedia" & vbCrLf & vbCrLf &
                   "⚠️ Untuk testing, klik 'Simulasi Kartu Kredit' di bawah",
            .Font = New Font("Segoe UI", 11),
            .Location = New Point(20, 20),
            .Size = New Size(500, 160),
            .ForeColor = Color.FromArgb(44, 62, 80)
        }
        
        pnlCard.Controls.Add(lblCardInfo)
        Me.Controls.Add(pnlCard)
    End Sub
    
    Private Sub CreateActionButtons()
        ' Success Button
        Dim btnSuccess As New Button With {
            .Text = GetSuccessButtonText(),
            .Size = New Size(150, 45),
            .Location = New Point(100, 350),
            .BackColor = Color.FromArgb(46, 204, 113),
            .ForeColor = Color.White,
            .FlatStyle = FlatStyle.Flat,
            .Font = New Font("Segoe UI", 11, FontStyle.Bold),
            .Cursor = Cursors.Hand
        }
        btnSuccess.FlatAppearance.BorderSize = 0
        AddHandler btnSuccess.Click, AddressOf BtnSuccess_Click
        
        ' Fail Button
        Dim btnFail As New Button With {
            .Text = "❌ Simulasi Gagal",
            .Size = New Size(150, 45),
            .Location = New Point(270, 350),
            .BackColor = Color.FromArgb(231, 76, 60),
            .ForeColor = Color.White,
            .FlatStyle = FlatStyle.Flat,
            .Font = New Font("Segoe UI", 11, FontStyle.Bold),
            .Cursor = Cursors.Hand
        }
        btnFail.FlatAppearance.BorderSize = 0
        AddHandler btnFail.Click, AddressOf BtnFail_Click
        
        ' Cancel Button
        Dim btnCancel As New Button With {
            .Text = "🚫 Batal",
            .Size = New Size(100, 45),
            .Location = New Point(440, 350),
            .BackColor = Color.Gray,
            .ForeColor = Color.White,
            .FlatStyle = FlatStyle.Flat,
            .Font = New Font("Segoe UI", 11, FontStyle.Bold),
            .Cursor = Cursors.Hand
        }
        btnCancel.FlatAppearance.BorderSize = 0
        AddHandler btnCancel.Click, Sub() Me.Close()
        
        Me.Controls.AddRange({btnSuccess, btnFail, btnCancel})
    End Sub
    
    Private Function GetSuccessButtonText() As String
        Select Case paymentMethod
            Case "Transfer Bank"
                Return "✅ Simulasi Bayar"
            Case "COD (Cash on Delivery)"
                Return "✅ Konfirmasi COD"
            Case "E-Wallet"
                Return "✅ Simulasi E-Wallet"
            Case "Kartu Kredit"
                Return "✅ Simulasi Kartu"
            Case Else
                Return "✅ Simulasi Bayar"
        End Select
    End Function
    
    Private Sub BtnSuccess_Click(sender As Object, e As EventArgs)
        SimulatePaymentSuccess()
    End Sub
    
    Private Sub BtnFail_Click(sender As Object, e As EventArgs)
        SimulatePaymentFailure()
    End Sub
    
    Private Sub SimulatePaymentSuccess()
        Try
            koneksi()
            
            ' Update order status based on payment method
            Dim newStatus As String = If(paymentMethod = "COD (Cash on Delivery)", "processing", "processing")
            
            cmd = New MySqlCommand("UPDATE orders SET order_status = @status WHERE order_id = @id", conn)
            cmd.Parameters.AddWithValue("@status", newStatus)
            cmd.Parameters.AddWithValue("@id", orderID)
            cmd.ExecuteNonQuery()
            
            tutupKoneksi()
            
            ' Success message
            Dim successMsg As String = $"🎉 PEMBAYARAN BERHASIL!" & vbCrLf & vbCrLf &
                                      $"💳 Metode: {paymentMethod}" & vbCrLf &
                                      $"💰 Jumlah: Rp {totalAmount:N0}" & vbCrLf &
                                      $"📋 Order ID: #{orderID}" & vbCrLf &
                                      $"📦 Status: {If(newStatus = "processing", "Sedang Diproses", newStatus)}" & vbCrLf & vbCrLf &
                                      "✅ Pesanan Anda sedang diproses!" & vbCrLf &
                                      "📱 Cek status di menu 'Pesanan Saya'"
            
            MessageBox.Show(successMsg, "Pembayaran Berhasil", MessageBoxButtons.OK, MessageBoxIcon.Information)
            
            Me.DialogResult = DialogResult.OK
            Me.Close()
            
        Catch ex As Exception
            MessageBox.Show("Error updating payment status: " & ex.Message, "Error", MessageBoxButtons.OK, MessageBoxIcon.Error)
        End Try
    End Sub
    
    Private Sub SimulatePaymentFailure()
        Dim failMsg As String = $"❌ PEMBAYARAN GAGAL!" & vbCrLf & vbCrLf &
                               $"💳 Metode: {paymentMethod}" & vbCrLf &
                               $"💰 Jumlah: Rp {totalAmount:N0}" & vbCrLf & vbCrLf &
                               "🔧 Kemungkinan penyebab:" & vbCrLf &
                               "• Saldo tidak mencukupi" & vbCrLf &
                               "• Kartu expired atau diblokir" & vbCrLf &
                               "• Koneksi terputus" & vbCrLf &
                               "• Server payment gateway bermasalah" & vbCrLf & vbCrLf &
                               "💡 Silakan coba lagi atau gunakan metode lain"
        
        MessageBox.Show(failMsg, "Pembayaran Gagal", MessageBoxButtons.OK, MessageBoxIcon.Error)
        
        Me.DialogResult = DialogResult.Cancel
        Me.Close()
    End Sub
End Class
