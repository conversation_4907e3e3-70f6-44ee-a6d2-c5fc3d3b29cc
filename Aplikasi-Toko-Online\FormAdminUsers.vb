' FormAdminUsers.vb - Form khusus admin untuk mengelola users
Imports MySql.Data.MySqlClient

Public Class FormAdminUsers
    Private Sub FormAdminUsers_Load(sender As Object, e As EventArgs) Handles MyBase.Load
        Me.Text = "Admin - Kelola <PERSON>"
        Me.Size = New Size(1100, 650)
        Me.StartPosition = FormStartPosition.CenterScreen
        Me.BackColor = Color.FromArgb(248, 249, 250)

        CreateUI()
        LoadUsers()
    End Sub

    Private Sub CreateUI()
        ' Header Panel
        Dim pnlHeader As New Panel With {
            .Dock = DockStyle.Top,
            .Height = 80,
            .BackColor = Color.FromArgb(52, 58, 64)
        }

        Dim lblTitle As New Label With {
            .Text = "KELOLA PENGGUNA",
            .Font = New Font("Segoe UI", 20, FontStyle.Bold),
            .ForeColor = Color.White,
            .Location = New Point(20, 25),
            .AutoSize = True
        }

        Dim btnClose As New Button With {
            .Text = "✕",
            .Size = New Size(40, 40),
            .Location = New Point(Me.Width - 60, 20),
            .FlatStyle = FlatStyle.Flat,
            .BackColor = Color.FromArgb(220, 53, 69),
            .ForeColor = Color.White,
            .Font = New Font("Segoe UI", 16, FontStyle.Bold),
            .Cursor = Cursors.Hand
        }
        btnClose.FlatAppearance.BorderSize = 0
        AddHandler btnClose.Click, Sub() Me.Close()

        pnlHeader.Controls.AddRange({lblTitle, btnClose})

        ' Toolbar Panel
        Dim pnlToolbar As New Panel With {
            .Dock = DockStyle.Top,
            .Height = 60,
            .BackColor = Color.White,
            .Padding = New Padding(20, 10, 20, 10)
        }

        ' Search controls
        Dim lblSearch As New Label With {
            .Text = "Cari Username:",
            .Font = New Font("Segoe UI", 10),
            .Location = New Point(20, 18),
            .AutoSize = True
        }

        Dim txtSearch As New TextBox With {
            .Name = "txtSearch",
            .Size = New Size(180, 25),
            .Location = New Point(130, 15),
            .Font = New Font("Segoe UI", 10)
        }
        AddHandler txtSearch.TextChanged, AddressOf TxtSearch_TextChanged

        ' Role filter
        Dim lblRole As New Label With {
            .Text = "Role:",
            .Font = New Font("Segoe UI", 10),
            .Location = New Point(330, 18),
            .AutoSize = True
        }

        Dim cmbRole As New ComboBox With {
            .Name = "cmbRole",
            .Size = New Size(100, 25),
            .Location = New Point(370, 15),
            .DropDownStyle = ComboBoxStyle.DropDownList,
            .Font = New Font("Segoe UI", 10)
        }
        cmbRole.Items.AddRange({"Semua Role", "user", "admin"})
        cmbRole.SelectedIndex = 0
        AddHandler cmbRole.SelectedIndexChanged, AddressOf CmbRole_SelectedIndexChanged

        ' Action buttons
        Dim btnToggleRole As New Button With {
            .Text = "🔄 TOGGLE ADMIN",
            .Size = New Size(130, 35),
            .Location = New Point(490, 12),
            .FlatStyle = FlatStyle.Flat,
            .BackColor = Color.FromArgb(255, 193, 7),
            .ForeColor = Color.White,
            .Font = New Font("Segoe UI", 10, FontStyle.Bold),
            .Cursor = Cursors.Hand
        }
        btnToggleRole.FlatAppearance.BorderSize = 0
        AddHandler btnToggleRole.Click, AddressOf BtnToggleRole_Click

        Dim btnResetPassword As New Button With {
            .Text = "🔑 RESET PWD",
            .Size = New Size(110, 35),
            .Location = New Point(630, 12),
            .FlatStyle = FlatStyle.Flat,
            .BackColor = Color.FromArgb(220, 53, 69),
            .ForeColor = Color.White,
            .Font = New Font("Segoe UI", 10, FontStyle.Bold),
            .Cursor = Cursors.Hand
        }
        btnResetPassword.FlatAppearance.BorderSize = 0
        AddHandler btnResetPassword.Click, AddressOf BtnResetPassword_Click

        Dim btnRefresh As New Button With {
            .Text = "🔄 REFRESH",
            .Size = New Size(100, 35),
            .Location = New Point(750, 12),
            .FlatStyle = FlatStyle.Flat,
            .BackColor = Color.FromArgb(108, 117, 125),
            .ForeColor = Color.White,
            .Font = New Font("Segoe UI", 10, FontStyle.Bold),
            .Cursor = Cursors.Hand
        }
        btnRefresh.FlatAppearance.BorderSize = 0
        AddHandler btnRefresh.Click, Sub() LoadUsers()

        pnlToolbar.Controls.AddRange({lblSearch, txtSearch, lblRole, cmbRole, btnToggleRole, btnResetPassword, btnRefresh})

        ' Main Panel for DataGridView
        Dim pnlMain As New Panel With {
            .Dock = DockStyle.Fill,
            .Padding = New Padding(20)
        }

        ' DataGridView
        Dim dgvUsers As New DataGridView With {
            .Name = "dgvUsers",
            .Dock = DockStyle.Fill,
            .AutoSizeColumnsMode = DataGridViewAutoSizeColumnsMode.Fill,
            .AllowUserToAddRows = False,
            .AllowUserToDeleteRows = False,
            .SelectionMode = DataGridViewSelectionMode.FullRowSelect,
            .ReadOnly = True,
            .BackgroundColor = Color.White,
            .BorderStyle = BorderStyle.None,
            .RowHeadersVisible = False,
            .Font = New Font("Segoe UI", 10),
            .RowTemplate = New DataGridViewRow With {.Height = 40}
        }

        ' Style the DataGridView
        dgvUsers.DefaultCellStyle.SelectionBackColor = Color.FromArgb(52, 152, 219)
        dgvUsers.DefaultCellStyle.SelectionForeColor = Color.White
        dgvUsers.DefaultCellStyle.Padding = New Padding(5)
        dgvUsers.ColumnHeadersDefaultCellStyle.BackColor = Color.FromArgb(52, 58, 64)
        dgvUsers.ColumnHeadersDefaultCellStyle.ForeColor = Color.White
        dgvUsers.ColumnHeadersDefaultCellStyle.Font = New Font("Segoe UI", 11, FontStyle.Bold)
        dgvUsers.ColumnHeadersHeight = 45
        dgvUsers.EnableHeadersVisualStyles = False

        pnlMain.Controls.Add(dgvUsers)

        ' Status Panel
        Dim pnlStatus As New Panel With {
            .Dock = DockStyle.Bottom,
            .Height = 30,
            .BackColor = Color.FromArgb(248, 249, 250)
        }

        Dim lblStatus As New Label With {
            .Name = "lblStatus",
            .Text = "Siap",
            .Font = New Font("Segoe UI", 9),
            .ForeColor = Color.FromArgb(108, 117, 125),
            .Location = New Point(20, 8),
            .AutoSize = True
        }

        pnlStatus.Controls.Add(lblStatus)

        Me.Controls.AddRange({pnlMain, pnlStatus, pnlToolbar, pnlHeader})
    End Sub

    Private Sub LoadUsers(Optional searchText As String = "", Optional roleFilter As String = "")
        Try
            koneksi()

            Dim query As String = "SELECT user_id as 'ID', username as 'Username', email as 'Email', " &
                                 "full_name as 'Nama Lengkap', role as 'Role', " &
                                 "created_at as 'Tanggal Daftar' FROM users WHERE 1=1 "

            If searchText <> "" Then
                query &= "AND username LIKE '%" & searchText & "%' "
            End If

            If roleFilter <> "" AndAlso roleFilter <> "Semua Role" Then
                query &= "AND role = '" & roleFilter & "' "
            End If

            query &= "ORDER BY created_at DESC"

            cmd = New MySqlCommand(query, conn)
            Dim adapter As New MySqlDataAdapter(cmd)
            Dim dt As New DataTable()
            adapter.Fill(dt)

            Dim dgv As DataGridView = CType(Me.Controls.Find("dgvUsers", True).FirstOrDefault(), DataGridView)
            If dgv IsNot Nothing Then
                dgv.DataSource = dt

                ' Format columns
                If dgv.Columns.Contains("Tanggal Daftar") Then
                    dgv.Columns("Tanggal Daftar").DefaultCellStyle.Format = "dd/MM/yyyy"
                End If

                If dgv.Columns.Contains("ID") Then
                    dgv.Columns("ID").Width = 50
                End If

                ' Color code roles
                For Each row As DataGridViewRow In dgv.Rows
                    If row.Cells("Role").Value IsNot Nothing Then
                        Dim role As String = row.Cells("Role").Value.ToString().ToLower()
                        If role = "admin" Then
                            row.Cells("Role").Style.BackColor = Color.FromArgb(220, 53, 69)
                            row.Cells("Role").Style.ForeColor = Color.White
                        Else
                            row.Cells("Role").Style.BackColor = Color.FromArgb(40, 167, 69)
                            row.Cells("Role").Style.ForeColor = Color.White
                        End If
                    End If
                Next
            End If

            ' Update status
            Dim lblStatus As Label = CType(Me.Controls.Find("lblStatus", True).FirstOrDefault(), Label)
            If lblStatus IsNot Nothing Then
                lblStatus.Text = $"Total: {dt.Rows.Count} pengguna"
            End If

            tutupKoneksi()

        Catch ex As Exception
            MessageBox.Show("Error loading users: " & ex.Message, "Error", MessageBoxButtons.OK, MessageBoxIcon.Error)
        End Try
    End Sub

    Private Sub TxtSearch_TextChanged(sender As Object, e As EventArgs)
        Dim searchText As String = CType(sender, TextBox).Text.Trim()
        Dim cmbRole As ComboBox = CType(Me.Controls.Find("cmbRole", True).FirstOrDefault(), ComboBox)
        Dim roleFilter As String = If(cmbRole IsNot Nothing, cmbRole.Text, "")
        LoadUsers(searchText, roleFilter)
    End Sub

    Private Sub CmbRole_SelectedIndexChanged(sender As Object, e As EventArgs)
        Dim roleFilter As String = CType(sender, ComboBox).Text
        Dim txtSearch As TextBox = CType(Me.Controls.Find("txtSearch", True).FirstOrDefault(), TextBox)
        Dim searchText As String = If(txtSearch IsNot Nothing, txtSearch.Text.Trim(), "")
        LoadUsers(searchText, roleFilter)
    End Sub

    Private Sub BtnToggleRole_Click(sender As Object, e As EventArgs)
        Dim dgv As DataGridView = CType(Me.Controls.Find("dgvUsers", True).FirstOrDefault(), DataGridView)

        If dgv Is Nothing OrElse dgv.SelectedRows.Count = 0 Then
            MessageBox.Show("Pilih pengguna yang akan diubah rolenya!", "Peringatan", MessageBoxButtons.OK, MessageBoxIcon.Warning)
            Return
        End If

        Dim userID As Integer = Convert.ToInt32(dgv.SelectedRows(0).Cells("ID").Value)
        Dim username As String = dgv.SelectedRows(0).Cells("Username").Value.ToString()
        Dim currentRole As String = dgv.SelectedRows(0).Cells("Role").Value.ToString()
        Dim newRole As String = If(currentRole.ToLower() = "admin", "user", "admin")

        If MessageBox.Show($"Yakin ingin mengubah role '{username}' dari '{currentRole}' menjadi '{newRole}'?",
                          "Konfirmasi Ubah Role", MessageBoxButtons.YesNo, MessageBoxIcon.Question) = DialogResult.Yes Then
            Try
                koneksi()
                cmd = New MySqlCommand("UPDATE users SET role = @role WHERE user_id = @id", conn)
                cmd.Parameters.AddWithValue("@role", newRole)
                cmd.Parameters.AddWithValue("@id", userID)
                cmd.ExecuteNonQuery()
                tutupKoneksi()

                MessageBox.Show($"Role pengguna '{username}' berhasil diubah menjadi '{newRole}'!", "Sukses", MessageBoxButtons.OK, MessageBoxIcon.Information)
                LoadUsers()

            Catch ex As Exception
                MessageBox.Show("Error mengubah role: " & ex.Message, "Error", MessageBoxButtons.OK, MessageBoxIcon.Error)
            End Try
        End If
    End Sub

    Private Sub BtnResetPassword_Click(sender As Object, e As EventArgs)
        Dim dgv As DataGridView = CType(Me.Controls.Find("dgvUsers", True).FirstOrDefault(), DataGridView)

        If dgv Is Nothing OrElse dgv.SelectedRows.Count = 0 Then
            MessageBox.Show("Pilih pengguna yang akan direset passwordnya!", "Peringatan", MessageBoxButtons.OK, MessageBoxIcon.Warning)
            Return
        End If

        Dim userID As Integer = Convert.ToInt32(dgv.SelectedRows(0).Cells("ID").Value)
        Dim username As String = dgv.SelectedRows(0).Cells("Username").Value.ToString()

        ' Create password reset dialog
        Dim resetForm As New Form With {
            .Text = "Reset Password",
            .Size = New Size(400, 250),
            .StartPosition = FormStartPosition.CenterParent,
            .FormBorderStyle = FormBorderStyle.FixedDialog,
            .MaximizeBox = False,
            .MinimizeBox = False
        }

        Dim lblUser As New Label With {
            .Text = $"Reset password untuk: {username}",
            .Font = New Font("Segoe UI", 12, FontStyle.Bold),
            .Location = New Point(20, 20),
            .AutoSize = True
        }

        Dim lblNewPassword As New Label With {
            .Text = "Password baru:",
            .Font = New Font("Segoe UI", 10),
            .Location = New Point(20, 60),
            .AutoSize = True
        }

        Dim txtNewPassword As New TextBox With {
            .Size = New Size(250, 25),
            .Location = New Point(20, 85),
            .Font = New Font("Segoe UI", 10),
            .UseSystemPasswordChar = True
        }

        Dim lblConfirmPassword As New Label With {
            .Text = "Konfirmasi password:",
            .Font = New Font("Segoe UI", 10),
            .Location = New Point(20, 120),
            .AutoSize = True
        }

        Dim txtConfirmPassword As New TextBox With {
            .Size = New Size(250, 25),
            .Location = New Point(20, 145),
            .Font = New Font("Segoe UI", 10),
            .UseSystemPasswordChar = True
        }

        Dim btnReset As New Button With {
            .Text = "RESET",
            .Size = New Size(80, 30),
            .Location = New Point(190, 185),
            .BackColor = Color.FromArgb(220, 53, 69),
            .ForeColor = Color.White,
            .FlatStyle = FlatStyle.Flat
        }
        btnReset.FlatAppearance.BorderSize = 0

        Dim btnCancel As New Button With {
            .Text = "BATAL",
            .Size = New Size(80, 30),
            .Location = New Point(280, 185),
            .BackColor = Color.FromArgb(108, 117, 125),
            .ForeColor = Color.White,
            .FlatStyle = FlatStyle.Flat
        }
        btnCancel.FlatAppearance.BorderSize = 0

        AddHandler btnReset.Click, Sub()
                                       If txtNewPassword.Text.Trim() = "" Then
                                           MessageBox.Show("Password tidak boleh kosong!", "Validasi", MessageBoxButtons.OK, MessageBoxIcon.Warning)
                                           Return
                                       End If

                                       If txtNewPassword.Text <> txtConfirmPassword.Text Then
                                           MessageBox.Show("Konfirmasi password tidak cocok!", "Validasi", MessageBoxButtons.OK, MessageBoxIcon.Warning)
                                           Return
                                       End If

                                       Try
                                           koneksi()
                                           cmd = New MySqlCommand("UPDATE users SET password = @password WHERE user_id = @id", conn)
                                           cmd.Parameters.AddWithValue("@password", txtNewPassword.Text) ' In real app, hash this password
                                           cmd.Parameters.AddWithValue("@id", userID)
                                           cmd.ExecuteNonQuery()
                                           tutupKoneksi()

                                           MessageBox.Show($"Password untuk '{username}' berhasil direset!", "Sukses", MessageBoxButtons.OK, MessageBoxIcon.Information)
                                           resetForm.Close()

                                       Catch ex As Exception
                                           MessageBox.Show("Error reset password: " & ex.Message, "Error", MessageBoxButtons.OK, MessageBoxIcon.Error)
                                       End Try
                                   End Sub

        AddHandler btnCancel.Click, Sub() resetForm.Close()

        resetForm.Controls.AddRange({lblUser, lblNewPassword, txtNewPassword, lblConfirmPassword, txtConfirmPassword, btnReset, btnCancel})
        resetForm.ShowDialog()
    End Sub
End Class
