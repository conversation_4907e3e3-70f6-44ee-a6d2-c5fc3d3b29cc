﻿' FormRegister.vb - Compatible dengan Designer
Imports MySql.Data.MySqlClient

Public Class FormRegister
    Private Sub FormRegister_Load(sender As Object, e As EventArgs) Handles MyBase.Load
        ' Setup form properties
        Me.Text = "Registrasi - Toko Online"
        Me.Size = New Size(450, 650)
        Me.StartPosition = FormStartPosition.CenterScreen
        Me.FormBorderStyle = FormBorderStyle.FixedDialog
        Me.MaximizeBox = False

        ' Create UI
        SetupRegisterUI()
    End Sub

    Private Sub SetupRegisterUI()
        ' Clear existing controls
        Me.Controls.Clear()

        ' Header
        Dim lblTitle As New Label With {
            .Text = "REGISTRASI AKUN BARU",
            .Font = New Font("Segoe UI", 16, FontStyle.Bold),
            .ForeColor = Color.FromArgb(52, 73, 94),
            .Location = New Point(80, 20),
            .AutoSize = True
        }

        ' Panel form
        Dim pnlForm As New Panel With {
            .Size = New Size(380, 500),
            .Location = New Point(35, 60),
            .AutoScroll = True,
            .BackColor = Color.Transparent
        }

        Dim yPos As Integer = 10

        ' Username
        Dim lblUsername As New Label With {
            .Text = "Username *",
            .Location = New Point(0, yPos),
            .AutoSize = True,
            .Font = New Font("Segoe UI", 10)
        }
        yPos += 25

        Dim txtUsername As New TextBox With {
            .Name = "txtUsername",
            .Size = New Size(350, 30),
            .Location = New Point(0, yPos),
            .Font = New Font("Segoe UI", 10)
        }
        yPos += 40

        ' Password
        Dim lblPassword As New Label With {
            .Text = "Password * (minimal 6 karakter)",
            .Location = New Point(0, yPos),
            .AutoSize = True,
            .Font = New Font("Segoe UI", 10)
        }
        yPos += 25

        Dim txtPassword As New TextBox With {
            .Name = "txtPassword",
            .Size = New Size(350, 30),
            .Location = New Point(0, yPos),
            .UseSystemPasswordChar = True,
            .Font = New Font("Segoe UI", 10)
        }
        yPos += 40

        ' Confirm Password
        Dim lblConfirmPassword As New Label With {
            .Text = "Konfirmasi Password *",
            .Location = New Point(0, yPos),
            .AutoSize = True,
            .Font = New Font("Segoe UI", 10)
        }
        yPos += 25

        Dim txtConfirmPassword As New TextBox With {
            .Name = "txtConfirmPassword",
            .Size = New Size(350, 30),
            .Location = New Point(0, yPos),
            .UseSystemPasswordChar = True,
            .Font = New Font("Segoe UI", 10)
        }
        yPos += 40

        ' Full Name
        Dim lblFullName As New Label With {
            .Text = "Nama Lengkap *",
            .Location = New Point(0, yPos),
            .AutoSize = True,
            .Font = New Font("Segoe UI", 10)
        }
        yPos += 25

        Dim txtFullName As New TextBox With {
            .Name = "txtFullName",
            .Size = New Size(350, 30),
            .Location = New Point(0, yPos),
            .Font = New Font("Segoe UI", 10)
        }
        yPos += 40

        ' Email
        Dim lblEmail As New Label With {
            .Text = "Email *",
            .Location = New Point(0, yPos),
            .AutoSize = True,
            .Font = New Font("Segoe UI", 10)
        }
        yPos += 25

        Dim txtEmail As New TextBox With {
            .Name = "txtEmail",
            .Size = New Size(350, 30),
            .Location = New Point(0, yPos),
            .Font = New Font("Segoe UI", 10)
        }
        yPos += 40

        ' Phone
        Dim lblPhone As New Label With {
            .Text = "No. Telepon (opsional)",
            .Location = New Point(0, yPos),
            .AutoSize = True,
            .Font = New Font("Segoe UI", 10)
        }
        yPos += 25

        Dim txtPhone As New TextBox With {
            .Name = "txtPhone",
            .Size = New Size(350, 30),
            .Location = New Point(0, yPos),
            .Font = New Font("Segoe UI", 10)
        }
        yPos += 40

        ' Address
        Dim lblAddress As New Label With {
            .Text = "Alamat (opsional)",
            .Location = New Point(0, yPos),
            .AutoSize = True,
            .Font = New Font("Segoe UI", 10)
        }
        yPos += 25

        Dim txtAddress As New TextBox With {
            .Name = "txtAddress",
            .Size = New Size(350, 60),
            .Location = New Point(0, yPos),
            .Multiline = True,
            .Font = New Font("Segoe UI", 10)
        }
        yPos += 75

        ' Info text
        Dim lblInfo As New Label With {
            .Text = "* Field wajib diisi",
            .Location = New Point(0, yPos),
            .AutoSize = True,
            .Font = New Font("Segoe UI", 9),
            .ForeColor = Color.FromArgb(127, 140, 141)
        }
        yPos += 25

        ' Buttons
        Dim btnRegister As New Button With {
            .Text = "DAFTAR",
            .Size = New Size(170, 40),
            .Location = New Point(0, yPos),
            .BackColor = Color.FromArgb(46, 204, 113),
            .ForeColor = Color.White,
            .FlatStyle = FlatStyle.Flat,
            .Font = New Font("Segoe UI", 10, FontStyle.Bold),
            .Cursor = Cursors.Hand
        }
        btnRegister.FlatAppearance.BorderSize = 0
        AddHandler btnRegister.Click, AddressOf BtnRegister_Click

        Dim btnCancel As New Button With {
            .Text = "BATAL",
            .Size = New Size(170, 40),
            .Location = New Point(180, yPos),
            .BackColor = Color.FromArgb(231, 76, 60),
            .ForeColor = Color.White,
            .FlatStyle = FlatStyle.Flat,
            .Font = New Font("Segoe UI", 10, FontStyle.Bold),
            .Cursor = Cursors.Hand
        }
        btnCancel.FlatAppearance.BorderSize = 0
        AddHandler btnCancel.Click, Sub() Me.Close()

        pnlForm.Controls.AddRange({lblUsername, txtUsername, lblPassword, txtPassword,
                                  lblConfirmPassword, txtConfirmPassword, lblFullName, txtFullName,
                                  lblEmail, txtEmail, lblPhone, txtPhone, lblAddress, txtAddress,
                                  lblInfo, btnRegister, btnCancel})

        Me.Controls.AddRange({lblTitle, pnlForm})
    End Sub

    Private Sub BtnRegister_Click(sender As Object, e As EventArgs)
        ' Get controls
        Dim txtUsername = CType(Me.Controls.Find("txtUsername", True).FirstOrDefault(), TextBox)
        Dim txtPassword = CType(Me.Controls.Find("txtPassword", True).FirstOrDefault(), TextBox)
        Dim txtConfirmPassword = CType(Me.Controls.Find("txtConfirmPassword", True).FirstOrDefault(), TextBox)
        Dim txtFullName = CType(Me.Controls.Find("txtFullName", True).FirstOrDefault(), TextBox)
        Dim txtEmail = CType(Me.Controls.Find("txtEmail", True).FirstOrDefault(), TextBox)
        Dim txtPhone = CType(Me.Controls.Find("txtPhone", True).FirstOrDefault(), TextBox)
        Dim txtAddress = CType(Me.Controls.Find("txtAddress", True).FirstOrDefault(), TextBox)

        ' Check if controls exist
        If txtUsername Is Nothing OrElse txtPassword Is Nothing Then
            MessageBox.Show("Error: Controls not found!", "Error", MessageBoxButtons.OK, MessageBoxIcon.Error)
            Return
        End If

        ' Get values
        Dim username As String = txtUsername.Text.Trim()
        Dim password As String = txtPassword.Text
        Dim confirmPassword As String = txtConfirmPassword.Text
        Dim fullName As String = txtFullName.Text.Trim()
        Dim email As String = txtEmail.Text.Trim()
        Dim phone As String = If(txtPhone IsNot Nothing, txtPhone.Text.Trim(), "")
        Dim address As String = If(txtAddress IsNot Nothing, txtAddress.Text.Trim(), "")

        ' Validasi field wajib
        If username = "" Or password = "" Or fullName = "" Or email = "" Then
            MessageBox.Show("Field bertanda * wajib diisi!", "Peringatan", MessageBoxButtons.OK, MessageBoxIcon.Warning)
            Return
        End If

        ' Validasi password
        If password <> confirmPassword Then
            MessageBox.Show("Password dan konfirmasi password tidak sama!", "Peringatan", MessageBoxButtons.OK, MessageBoxIcon.Warning)
            Return
        End If

        If password.Length < 6 Then
            MessageBox.Show("Password minimal 6 karakter!", "Peringatan", MessageBoxButtons.OK, MessageBoxIcon.Warning)
            Return
        End If

        ' Validasi email
        If Not email.Contains("@") Or Not email.Contains(".") Then
            MessageBox.Show("Format email tidak valid!", "Peringatan", MessageBoxButtons.OK, MessageBoxIcon.Warning)
            Return
        End If

        Try
            koneksi()

            ' Cek username sudah ada atau belum
            cmd = New MySqlCommand("SELECT COUNT(*) FROM users WHERE username = @username", conn)
            cmd.Parameters.AddWithValue("@username", username)
            Dim count As Integer = Convert.ToInt32(cmd.ExecuteScalar())

            If count > 0 Then
                MessageBox.Show("Username sudah digunakan!", "Peringatan", MessageBoxButtons.OK, MessageBoxIcon.Warning)
                tutupKoneksi()
                Return
            End If

            ' Insert user baru
            cmd = New MySqlCommand("INSERT INTO users (username, password, full_name, email, phone, address, created_at) " &
                                  "VALUES (@username, @password, @fullName, @email, @phone, @address, NOW())", conn)
            cmd.Parameters.AddWithValue("@username", username)
            cmd.Parameters.AddWithValue("@password", password)
            cmd.Parameters.AddWithValue("@fullName", fullName)
            cmd.Parameters.AddWithValue("@email", email)
            cmd.Parameters.AddWithValue("@phone", phone)
            cmd.Parameters.AddWithValue("@address", address)

            cmd.ExecuteNonQuery()
            tutupKoneksi()

            MessageBox.Show("Registrasi berhasil!" & vbCrLf & vbCrLf &
                          "Silakan login dengan akun Anda.", "Sukses",
                          MessageBoxButtons.OK, MessageBoxIcon.Information)
            Me.Close()

        Catch ex As Exception
            MessageBox.Show("Error: " & ex.Message, "Error", MessageBoxButtons.OK, MessageBoxIcon.Error)
            tutupKoneksi()
        End Try
    End Sub
End Class