Imports MySql.Data.MySqlClient
Imports System.Drawing

Public Class FormOrderDetail
    Private orderID As Integer
    
    Public Sub New(orderID As Integer)
        InitializeComponent()
        Me.orderID = orderID
    End Sub
    
    Private Sub FormOrderDetail_Load(sender As Object, e As EventArgs) Handles MyBase.Load
        Me.Text = $"Detail Pesanan #{orderID}"
        Me.Size = New Size(800, 600)
        Me.StartPosition = FormStartPosition.CenterScreen
        Me.BackColor = Color.FromArgb(245, 245, 245)

        ' Set form properties untuk tidak bisa di-minimize/maximize
        Me.FormBorderStyle = FormBorderStyle.FixedSingle
        Me.MaximizeBox = False
        Me.MinimizeBox = False
        Me.MinimumSize = New Size(800, 600)
        Me.MaximumSize = New Size(800, 600)

        CreateUI()
        LoadOrderDetail()
    End Sub
    
    Private Sub CreateUI()
        ' Header Panel - POSISI ABSOLUT
        Dim pnlHeader As New Panel With {
            .Size = New Size(<PERSON><PERSON>Width, 80),
            .Location = New Point(0, 0),
            .BackColor = Color.FromArgb(52, 152, 219),
            .Anchor = AnchorStyles.Top Or AnchorStyles.Left Or AnchorStyles.Right
        }
        
        Dim lblTitle As New Label With {
            .Text = $"DETAIL PESANAN #{orderID}",
            .Font = New Font("Segoe UI", 16, FontStyle.Bold),
            .ForeColor = Color.White,
            .Location = New Point(30, 25),
            .AutoSize = True
        }
        
        pnlHeader.Controls.Add(lblTitle)

        ' Main Panel with ScrollBar - POSISI ABSOLUT
        Dim pnlMain As New Panel With {
            .Name = "pnlMain",
            .Location = New Point(0, 80),
            .Size = New Size(Me.Width, Me.Height - 80),
            .BackColor = Color.FromArgb(245, 245, 245),
            .AutoScroll = True,
            .Anchor = AnchorStyles.Top Or AnchorStyles.Left Or AnchorStyles.Right Or AnchorStyles.Bottom
        }

        ' TAMBAHKAN KONTROL DALAM URUTAN YANG BENAR
        Me.Controls.Add(pnlMain)   ' Tambahkan dulu main panel
        Me.Controls.Add(pnlHeader) ' Kemudian header (akan di atas)
    End Sub
    
    Private Sub LoadOrderDetail()
        Try
            koneksi()
            
            Dim pnlMain As Panel = CType(Me.Controls("pnlMain"), Panel)
            pnlMain.Controls.Clear()
            
            ' Load Order Info
            cmd = New MySqlCommand("SELECT o.*, u.username FROM orders o " &
                                  "JOIN users u ON o.user_id = u.user_id " &
                                  "WHERE o.order_id = @orderID", conn)
            cmd.Parameters.AddWithValue("@orderID", orderID)
            dr = cmd.ExecuteReader()
            
            If dr.Read() Then
                Dim orderInfo As Panel = CreateOrderInfoPanel(dr)
                orderInfo.Location = New Point(20, 20)
                pnlMain.Controls.Add(orderInfo)
                
                dr.Close()
                
                ' Load Order Items
                Dim itemsPanel As Panel = CreateOrderItemsPanel()
                itemsPanel.Location = New Point(20, 280)
                pnlMain.Controls.Add(itemsPanel)
            Else
                dr.Close()
                MessageBox.Show("Pesanan tidak ditemukan!", "Error", MessageBoxButtons.OK, MessageBoxIcon.Error)
                Me.Close()
            End If
            
            tutupKoneksi()
            
        Catch ex As Exception
            MessageBox.Show("Error loading order detail: " & ex.Message, "Error", MessageBoxButtons.OK, MessageBoxIcon.Error)
        End Try
    End Sub
    
    Private Function CreateOrderInfoPanel(dr As MySqlDataReader) As Panel
        Dim panel As New Panel With {
            .Size = New Size(740, 240),
            .BackColor = Color.White,
            .BorderStyle = BorderStyle.FixedSingle
        }
        
        ' Order Information
        Dim lblOrderInfo As New Label With {
            .Text = "INFORMASI PESANAN",
            .Font = New Font("Segoe UI", 12, FontStyle.Bold),
            .Location = New Point(20, 15),
            .AutoSize = True,
            .BackColor = Color.Transparent
        }

        Dim lblOrderDate As New Label With {
            .Text = $"Tanggal Pesanan: {Convert.ToDateTime(dr("order_date")):dd MMMM yyyy HH:mm}",
            .Font = New Font("Segoe UI", 10),
            .Location = New Point(20, 45),
            .AutoSize = True,
            .BackColor = Color.Transparent
        }

        Dim lblCustomer As New Label With {
            .Text = $"Pelanggan: {dr("username")}",
            .Font = New Font("Segoe UI", 10),
            .Location = New Point(20, 70),
            .AutoSize = True,
            .BackColor = Color.Transparent
        }

        Dim lblStatus As New Label With {
            .Text = $"Status: {GetStatusText(dr("order_status").ToString())}",
            .Font = New Font("Segoe UI", 10, FontStyle.Bold),
            .ForeColor = GetStatusColor(dr("order_status").ToString()),
            .Location = New Point(20, 95),
            .AutoSize = True,
            .BackColor = Color.Transparent
        }

        Dim lblTotal As New Label With {
            .Text = $"Total Pembayaran: Rp {Convert.ToDecimal(dr("total_amount")):N0}",
            .Font = New Font("Segoe UI", 12, FontStyle.Bold),
            .ForeColor = Color.FromArgb(52, 152, 219),
            .Location = New Point(20, 120),
            .AutoSize = True,
            .BackColor = Color.Transparent
        }
        
        ' Shipping Information
        Dim lblShippingInfo As New Label With {
            .Text = "INFORMASI PENGIRIMAN",
            .Font = New Font("Segoe UI", 12, FontStyle.Bold),
            .Location = New Point(400, 15),
            .AutoSize = True,
            .BackColor = Color.Transparent
        }

        Dim lblShippingAddress As New Label With {
            .Text = $"Alamat Pengiriman:",
            .Font = New Font("Segoe UI", 10, FontStyle.Bold),
            .Location = New Point(400, 45),
            .AutoSize = True,
            .BackColor = Color.Transparent
        }

        Dim lblAddress As New Label With {
            .Text = dr("shipping_address").ToString(),
            .Font = New Font("Segoe UI", 10),
            .Location = New Point(400, 70),
            .Size = New Size(320, 80),
            .AutoEllipsis = True,
            .BackColor = Color.Transparent
        }

        Dim lblPaymentMethod As New Label With {
            .Text = $"Metode Pembayaran: {If(IsDBNull(dr("payment_method")), "Belum dipilih", dr("payment_method").ToString())}",
            .Font = New Font("Segoe UI", 10),
            .Location = New Point(400, 160),
            .AutoSize = True,
            .BackColor = Color.Transparent
        }
        
        panel.Controls.AddRange({lblOrderInfo, lblOrderDate, lblCustomer, lblStatus, lblTotal,
                               lblShippingInfo, lblShippingAddress, lblAddress, lblPaymentMethod})
        
        Return panel
    End Function
    
    Private Function CreateOrderItemsPanel() As Panel
        Dim panel As New Panel With {
            .Size = New Size(740, 300),
            .BackColor = Color.White,
            .BorderStyle = BorderStyle.FixedSingle
        }
        
        Dim lblItemsTitle As New Label With {
            .Text = "ITEM PESANAN",
            .Font = New Font("Segoe UI", 12, FontStyle.Bold),
            .Location = New Point(20, 15),
            .AutoSize = True,
            .BackColor = Color.Transparent
        }
        
        panel.Controls.Add(lblItemsTitle)
        
        ' Create DataGridView for order items
        Dim dgvItems As New DataGridView With {
            .Name = "dgvItems",
            .Location = New Point(20, 50),
            .Size = New Size(700, 230),
            .BackgroundColor = Color.White,
            .BorderStyle = BorderStyle.None,
            .AllowUserToAddRows = False,
            .AllowUserToDeleteRows = False,
            .ReadOnly = True,
            .SelectionMode = DataGridViewSelectionMode.FullRowSelect,
            .AutoSizeColumnsMode = DataGridViewAutoSizeColumnsMode.Fill
        }
        
        ' Add columns
        dgvItems.Columns.Add("product_name", "Nama Produk")
        dgvItems.Columns.Add("price", "Harga")
        dgvItems.Columns.Add("quantity", "Jumlah")
        dgvItems.Columns.Add("subtotal", "Subtotal")
        
        ' Format columns
        dgvItems.Columns("price").DefaultCellStyle.Format = "C0"
        dgvItems.Columns("price").DefaultCellStyle.FormatProvider = New System.Globalization.CultureInfo("id-ID")
        dgvItems.Columns("subtotal").DefaultCellStyle.Format = "C0"
        dgvItems.Columns("subtotal").DefaultCellStyle.FormatProvider = New System.Globalization.CultureInfo("id-ID")
        dgvItems.Columns("quantity").DefaultCellStyle.Alignment = DataGridViewContentAlignment.MiddleCenter
        
        panel.Controls.Add(dgvItems)
        
        ' Load order items
        LoadOrderItems(dgvItems)
        
        Return panel
    End Function
    
    Private Sub LoadOrderItems(dgv As DataGridView)
        Try
            cmd = New MySqlCommand("SELECT od.*, p.product_name FROM order_details od " &
                                  "JOIN products p ON od.product_id = p.product_id " &
                                  "WHERE od.order_id = @orderID", conn)
            cmd.Parameters.AddWithValue("@orderID", orderID)
            dr = cmd.ExecuteReader()
            
            While dr.Read()
                Dim price As Decimal = Convert.ToDecimal(dr("price"))
                Dim quantity As Integer = Convert.ToInt32(dr("quantity"))
                Dim subtotal As Decimal

                ' Gunakan subtotal dari database jika ada, jika tidak hitung manual
                If Not IsDBNull(dr("subtotal")) Then
                    subtotal = Convert.ToDecimal(dr("subtotal"))
                Else
                    subtotal = price * quantity
                End If

                dgv.Rows.Add(
                    dr("product_name").ToString(),
                    price,
                    quantity,
                    subtotal
                )
            End While
            
            dr.Close()
            
        Catch ex As Exception
            MessageBox.Show("Error loading order items: " & ex.Message, "Error", MessageBoxButtons.OK, MessageBoxIcon.Error)
        End Try
    End Sub
    
    Private Function GetStatusText(status As String) As String
        Select Case status.ToLower()
            Case "pending"
                Return "Menunggu Konfirmasi"
            Case "processing"
                Return "Sedang Diproses"
            Case "shipped"
                Return "Sedang Dikirim"
            Case "delivered"
                Return "Telah Diterima"
            Case "cancelled"
                Return "Dibatalkan"
            Case Else
                Return status
        End Select
    End Function
    
    Private Function GetStatusColor(status As String) As Color
        Select Case status.ToLower()
            Case "pending"
                Return Color.FromArgb(241, 196, 15)
            Case "processing"
                Return Color.FromArgb(52, 152, 219)
            Case "shipped"
                Return Color.FromArgb(155, 89, 182)
            Case "delivered"
                Return Color.FromArgb(46, 204, 113)
            Case "cancelled"
                Return Color.FromArgb(231, 76, 60)
            Case Else
                Return Color.Gray
        End Select
    End Function
End Class
