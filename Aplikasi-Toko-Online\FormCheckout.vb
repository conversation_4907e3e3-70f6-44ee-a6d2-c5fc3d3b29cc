Imports MySql.Data.MySqlClient
Imports System.Drawing

Public Class FormCheckout
    Private cartItems As List(Of FormMain.CartItem)
    Private currentUserID As Integer
    Private totalAmount As Decimal
    
    Public Sub New(items As List(Of FormMain.CartItem), userID As Integer)
        InitializeComponent()
        cartItems = items
        currentUserID = userID
        totalAmount = items.Sum(Function(item) item.Total)
    End Sub
    
    Private Sub FormCheckout_Load(sender As Object, e As EventArgs) Handles MyBase.Load
        Me.Text = "Checkout"
        Me.Size = New Size(900, 750)
        Me.StartPosition = FormStartPosition.CenterScreen
        Me.BackColor = Color.FromArgb(248, 249, 250)

        ' Set form properties untuk ukuran absolute dan tidak bisa di-minimize/maximize
        Me.FormBorderStyle = FormBorderStyle.FixedSingle
        Me.MaximizeBox = False
        Me.MinimizeBox = False
        Me.MinimumSize = New Size(900, 750)
        Me.MaximumSize = New Size(900, 750)

        CreateUI()
        LoadUserInfo()
    End Sub
    
    Private Sub CreateUI()
        ' Header Panel dengan gradient
        Dim pnlHeader As New Panel With {
            .Size = New Size(Me.Width, 100),
            .Location = New Point(0, 0),
            .BackColor = Color.FromArgb(41, 128, 185),
            .Dock = DockStyle.Top
        }

        ' Gradient effect untuk header
        AddHandler pnlHeader.Paint, Sub(sender, e)
            Dim rect As New Rectangle(0, 0, pnlHeader.Width, pnlHeader.Height)
            Using brush As New Drawing2D.LinearGradientBrush(rect, Color.FromArgb(52, 152, 219), Color.FromArgb(41, 128, 185), Drawing2D.LinearGradientMode.Vertical)
                e.Graphics.FillRectangle(brush, rect)
            End Using
        End Sub

        Dim lblTitle As New Label With {
            .Text = "🛒 CHECKOUT",
            .Font = New Font("Segoe UI", 24, FontStyle.Bold),
            .ForeColor = Color.White,
            .Location = New Point(40, 30),
            .AutoSize = True
        }

        pnlHeader.Controls.Add(lblTitle)
        Me.Controls.Add(pnlHeader)
        
        ' Shipping Address Panel dengan styling modern
        Dim pnlShipping As New Panel With {
            .Location = New Point(40, 130),
            .Size = New Size(800, 180),
            .BackColor = Color.White,
            .BorderStyle = BorderStyle.None
        }

        ' Tambahkan border dan shadow
        AddHandler pnlShipping.Paint, Sub(sender, e)
            Dim rect As New Rectangle(0, 0, pnlShipping.Width - 1, pnlShipping.Height - 1)
            Using pen As New Pen(Color.FromArgb(220, 220, 220), 1)
                e.Graphics.DrawRectangle(pen, rect)
            End Using
        End Sub

        Dim lblShippingTitle As New Label With {
            .Text = "📍 ALAMAT PENGIRIMAN",
            .Font = New Font("Segoe UI", 14, FontStyle.Bold),
            .ForeColor = Color.FromArgb(52, 73, 94),
            .Location = New Point(25, 20),
            .AutoSize = True
        }

        Dim txtAddress As New TextBox With {
            .Name = "txtAddress",
            .Location = New Point(25, 60),
            .Size = New Size(750, 90),
            .Multiline = True,
            .Font = New Font("Segoe UI", 12),
            .BorderStyle = BorderStyle.FixedSingle,
            .BackColor = Color.FromArgb(250, 250, 250)
        }
        
        pnlShipping.Controls.AddRange({lblShippingTitle, txtAddress})
        Me.Controls.Add(pnlShipping)
        
        ' Payment Method Panel dengan styling modern
        Dim pnlPayment As New Panel With {
            .Location = New Point(40, 330),
            .Size = New Size(800, 140),
            .BackColor = Color.White,
            .BorderStyle = BorderStyle.None
        }

        ' Tambahkan border dan shadow
        AddHandler pnlPayment.Paint, Sub(sender, e)
            Dim rect As New Rectangle(0, 0, pnlPayment.Width - 1, pnlPayment.Height - 1)
            Using pen As New Pen(Color.FromArgb(220, 220, 220), 1)
                e.Graphics.DrawRectangle(pen, rect)
            End Using
        End Sub

        Dim lblPaymentTitle As New Label With {
            .Text = "💳 METODE PEMBAYARAN",
            .Font = New Font("Segoe UI", 14, FontStyle.Bold),
            .ForeColor = Color.FromArgb(52, 73, 94),
            .Location = New Point(25, 20),
            .AutoSize = True
        }

        Dim cmbPayment As New ComboBox With {
            .Name = "cmbPayment",
            .Location = New Point(25, 60),
            .Size = New Size(300, 35),
            .DropDownStyle = ComboBoxStyle.DropDownList,
            .Font = New Font("Segoe UI", 12),
            .FlatStyle = FlatStyle.Flat
        }
        cmbPayment.Items.AddRange({"Transfer Bank", "COD (Cash on Delivery)", "E-Wallet", "Kartu Kredit"})
        cmbPayment.SelectedIndex = 0
        
        pnlPayment.Controls.AddRange({lblPaymentTitle, cmbPayment})
        Me.Controls.Add(pnlPayment)
        
        ' Order Summary Panel dengan styling modern
        Dim pnlSummary As New Panel With {
            .Location = New Point(40, 490),
            .Size = New Size(800, 120),
            .BackColor = Color.White,
            .BorderStyle = BorderStyle.None
        }

        ' Tambahkan border dan shadow
        AddHandler pnlSummary.Paint, Sub(sender, e)
            Dim rect As New Rectangle(0, 0, pnlSummary.Width - 1, pnlSummary.Height - 1)
            Using pen As New Pen(Color.FromArgb(220, 220, 220), 1)
                e.Graphics.DrawRectangle(pen, rect)
            End Using
        End Sub

        Dim lblSummaryTitle As New Label With {
            .Text = "📋 RINGKASAN PESANAN",
            .Font = New Font("Segoe UI", 14, FontStyle.Bold),
            .ForeColor = Color.FromArgb(52, 73, 94),
            .Location = New Point(25, 20),
            .AutoSize = True
        }

        Dim lblItemCount As New Label With {
            .Text = $"Jumlah Item: {cartItems.Sum(Function(item) item.Quantity)}",
            .Font = New Font("Segoe UI", 12),
            .ForeColor = Color.FromArgb(127, 140, 141),
            .Location = New Point(25, 55),
            .AutoSize = True
        }

        Dim lblTotal As New Label With {
            .Text = $"Total Pembayaran: Rp {totalAmount:N0}",
            .Font = New Font("Segoe UI", 16, FontStyle.Bold),
            .ForeColor = Color.FromArgb(52, 152, 219),
            .Location = New Point(25, 80),
            .AutoSize = True
        }
        
        pnlSummary.Controls.AddRange({lblSummaryTitle, lblItemCount, lblTotal})
        Me.Controls.Add(pnlSummary)
        
        ' Action Buttons dengan styling modern - posisi tengah
        Dim btnCancel As New Button With {
            .Text = "❌ Batal",
            .Size = New Size(150, 50),
            .Location = New Point(280, 630),
            .BackColor = Color.FromArgb(149, 165, 166),
            .ForeColor = Color.White,
            .FlatStyle = FlatStyle.Flat,
            .Font = New Font("Segoe UI", 12, FontStyle.Bold),
            .Cursor = Cursors.Hand
        }
        btnCancel.FlatAppearance.BorderSize = 0
        AddHandler btnCancel.Click, Sub() Me.Close()

        Dim btnPlaceOrder As New Button With {
            .Text = "✅ Buat Pesanan",
            .Size = New Size(180, 50),
            .Location = New Point(450, 630),
            .BackColor = Color.FromArgb(46, 204, 113),
            .ForeColor = Color.White,
            .FlatStyle = FlatStyle.Flat,
            .Font = New Font("Segoe UI", 12, FontStyle.Bold),
            .Cursor = Cursors.Hand
        }
        btnPlaceOrder.FlatAppearance.BorderSize = 0
        AddHandler btnPlaceOrder.Click, AddressOf BtnPlaceOrder_Click
        
        Me.Controls.AddRange({btnCancel, btnPlaceOrder})
    End Sub
    
    Private Sub LoadUserInfo()
        Try
            koneksi()
            
            cmd = New MySqlCommand("SELECT address FROM users WHERE user_id = @userID", conn)
            cmd.Parameters.AddWithValue("@userID", currentUserID)
            dr = cmd.ExecuteReader()
            
            If dr.Read() AndAlso Not IsDBNull(dr("address")) Then
                Dim txtAddress As TextBox = CType(Me.Controls.Find("txtAddress", True).FirstOrDefault(), TextBox)
                If txtAddress IsNot Nothing Then
                    txtAddress.Text = dr("address").ToString()
                    txtAddress.ForeColor = Color.Black
                End If
            Else
                Dim txtAddress As TextBox = CType(Me.Controls.Find("txtAddress", True).FirstOrDefault(), TextBox)
                If txtAddress IsNot Nothing Then
                    txtAddress.Text = "Masukkan alamat lengkap pengiriman..."
                    txtAddress.ForeColor = Color.Gray
                End If
            End If
            
            dr.Close()
            tutupKoneksi()
            
        Catch ex As Exception
            MessageBox.Show("Error loading user info: " & ex.Message, "Error", MessageBoxButtons.OK, MessageBoxIcon.Error)
        End Try
    End Sub
    
    Private Sub BtnPlaceOrder_Click(sender As Object, e As EventArgs)
        Dim txtAddress As TextBox = CType(Me.Controls.Find("txtAddress", True).FirstOrDefault(), TextBox)
        Dim cmbPayment As ComboBox = CType(Me.Controls.Find("cmbPayment", True).FirstOrDefault(), ComboBox)

        ' Validasi input dengan pesan yang lebih jelas
        If String.IsNullOrWhiteSpace(txtAddress.Text) Then
            MessageBox.Show("⚠️ Alamat pengiriman harus diisi!" & vbCrLf &
                           "Silakan masukkan alamat lengkap untuk pengiriman.",
                           "Alamat Diperlukan", MessageBoxButtons.OK, MessageBoxIcon.Warning)
            txtAddress.Focus()
            Return
        End If

        If cmbPayment.SelectedIndex = -1 Then
            MessageBox.Show("⚠️ Pilih metode pembayaran!" & vbCrLf &
                           "Silakan pilih cara pembayaran yang diinginkan.",
                           "Metode Pembayaran Diperlukan", MessageBoxButtons.OK, MessageBoxIcon.Warning)
            cmbPayment.Focus()
            Return
        End If

        ' Konfirmasi sebelum checkout
        Dim confirmMsg As String = $"🛒 KONFIRMASI PESANAN" & vbCrLf & vbCrLf &
                                  $"Total Pembayaran: Rp {totalAmount:N0}" & vbCrLf &
                                  $"Metode Pembayaran: {cmbPayment.SelectedItem}" & vbCrLf &
                                  $"Alamat Pengiriman: {txtAddress.Text}" & vbCrLf & vbCrLf &
                                  "Apakah Anda yakin ingin melanjutkan checkout?"

        If MessageBox.Show(confirmMsg, "Konfirmasi Checkout",
                          MessageBoxButtons.YesNo, MessageBoxIcon.Question) = DialogResult.No Then
            Return
        End If

        ' Disable button dan show loading
        Dim btnPlaceOrder As Button = CType(sender, Button)
        btnPlaceOrder.Enabled = False
        btnPlaceOrder.Text = "⏳ Memproses..."
        btnPlaceOrder.BackColor = Color.Gray
        Me.Cursor = Cursors.WaitCursor
        
        Try
            koneksi()
            Dim trans As MySqlTransaction = conn.BeginTransaction()

            Try
                ' Validasi data sebelum insert
                If currentUserID <= 0 Then
                    MessageBox.Show("Error: User ID tidak valid!", "Validasi Error", MessageBoxButtons.OK, MessageBoxIcon.Error)
                    Return
                End If

                If totalAmount <= 0 Then
                    MessageBox.Show("Error: Total amount tidak valid!", "Validasi Error", MessageBoxButtons.OK, MessageBoxIcon.Error)
                    Return
                End If

                ' Insert order (dengan payment_method)
                cmd = New MySqlCommand("INSERT INTO orders (user_id, order_date, total_amount, order_status, shipping_address, payment_method) " &
                                      "VALUES (@userID, @orderDate, @totalAmount, @status, @address, @payment)", conn, trans)
                cmd.Parameters.AddWithValue("@userID", currentUserID)
                cmd.Parameters.AddWithValue("@orderDate", DateTime.Now)
                cmd.Parameters.AddWithValue("@totalAmount", totalAmount)
                cmd.Parameters.AddWithValue("@status", "pending")
                cmd.Parameters.AddWithValue("@address", txtAddress.Text.Trim())
                cmd.Parameters.AddWithValue("@payment", cmbPayment.SelectedItem.ToString())
                cmd.ExecuteNonQuery()
                
                ' Get order ID
                Dim orderID As Integer = Convert.ToInt32(cmd.LastInsertedId)
                
                ' Insert order details (dengan subtotal)
                For Each item In cartItems
                    cmd = New MySqlCommand("INSERT INTO order_details (order_id, product_id, quantity, price, subtotal) " &
                                          "VALUES (@orderID, @productID, @quantity, @price, @subtotal)", conn, trans)
                    cmd.Parameters.AddWithValue("@orderID", orderID)
                    cmd.Parameters.AddWithValue("@productID", item.ProductID)
                    cmd.Parameters.AddWithValue("@quantity", item.Quantity)
                    cmd.Parameters.AddWithValue("@price", item.Price)
                    cmd.Parameters.AddWithValue("@subtotal", item.Total)
                    cmd.ExecuteNonQuery()
                    
                    ' Update product stock
                    cmd = New MySqlCommand("UPDATE products SET stock = stock - @quantity WHERE product_id = @productID", conn, trans)
                    cmd.Parameters.AddWithValue("@quantity", item.Quantity)
                    cmd.Parameters.AddWithValue("@productID", item.ProductID)
                    cmd.ExecuteNonQuery()
                Next
                
                trans.Commit()
                tutupKoneksi()

                ' Reset cursor dan button
                Me.Cursor = Cursors.Default
                btnPlaceOrder.Enabled = True
                btnPlaceOrder.Text = "✅ Buat Pesanan"
                btnPlaceOrder.BackColor = Color.FromArgb(46, 204, 113)

                ' Success message yang lebih informatif
                Dim successMsg As String = $"🎉 PESANAN BERHASIL DIBUAT!" & vbCrLf & vbCrLf &
                                          $"📋 ID Pesanan: #{orderID}" & vbCrLf &
                                          $"💰 Total Pembayaran: Rp {totalAmount:N0}" & vbCrLf &
                                          $"💳 Metode Pembayaran: {cmbPayment.SelectedItem}" & vbCrLf &
                                          $"📅 Tanggal: {DateTime.Now:dd/MM/yyyy HH:mm}" & vbCrLf &
                                          $"📦 Status: Menunggu Konfirmasi" & vbCrLf & vbCrLf &
                                          "✅ Pesanan Anda sedang diproses." & vbCrLf &
                                          "📧 Konfirmasi akan dikirim melalui email." & vbCrLf &
                                          "📱 Anda dapat melihat status pesanan di menu 'Pesanan Saya'."

                MessageBox.Show(successMsg, "Checkout Berhasil", MessageBoxButtons.OK, MessageBoxIcon.Information)

                Me.DialogResult = DialogResult.OK
                Me.Close()

            Catch ex As Exception
                trans.Rollback()

                ' Reset cursor dan button
                Me.Cursor = Cursors.Default
                btnPlaceOrder.Enabled = True
                btnPlaceOrder.Text = "✅ Buat Pesanan"
                btnPlaceOrder.BackColor = Color.FromArgb(46, 204, 113)

                ' Error message yang user-friendly
                Dim errorMsg As String = "❌ CHECKOUT GAGAL!" & vbCrLf & vbCrLf &
                                        "Maaf, terjadi kesalahan saat memproses pesanan Anda." & vbCrLf & vbCrLf &
                                        "🔧 Kemungkinan penyebab:" & vbCrLf &
                                        "• Koneksi internet bermasalah" & vbCrLf &
                                        "• Stok produk tidak mencukupi" & vbCrLf &
                                        "• Server sedang sibuk" & vbCrLf & vbCrLf &
                                        "💡 Silakan coba lagi dalam beberapa saat." & vbCrLf &
                                        "Jika masalah berlanjut, hubungi customer service."

                MessageBox.Show(errorMsg, "Checkout Gagal", MessageBoxButtons.OK, MessageBoxIcon.Error)
                Return
            End Try

        Catch ex As Exception
            ' Reset cursor dan button
            Me.Cursor = Cursors.Default
            Dim btnPlaceOrder As Button = CType(Me.Controls.Find("btnPlaceOrder", True).FirstOrDefault(), Button)
            If btnPlaceOrder IsNot Nothing Then
                btnPlaceOrder.Enabled = True
                btnPlaceOrder.Text = "✅ Buat Pesanan"
                btnPlaceOrder.BackColor = Color.FromArgb(46, 204, 113)
            End If

            ' Connection error message yang user-friendly
            Dim connectionErrorMsg As String = "🌐 KONEKSI BERMASALAH!" & vbCrLf & vbCrLf &
                                              "Tidak dapat terhubung ke server." & vbCrLf & vbCrLf &
                                              "🔧 Solusi yang dapat dicoba:" & vbCrLf &
                                              "• Periksa koneksi internet Anda" & vbCrLf &
                                              "• Restart aplikasi" & vbCrLf &
                                              "• Coba lagi dalam beberapa menit" & vbCrLf & vbCrLf &
                                              "💡 Jika masalah berlanjut, hubungi IT support."

            MessageBox.Show(connectionErrorMsg, "Koneksi Gagal", MessageBoxButtons.OK, MessageBoxIcon.Error)
        Finally
            If conn IsNot Nothing AndAlso conn.State = ConnectionState.Open Then
                tutupKoneksi()
            End If

            ' Pastikan cursor dan button kembali normal
            Me.Cursor = Cursors.Default
            Dim btnPlaceOrder As Button = CType(Me.Controls.Find("btnPlaceOrder", True).FirstOrDefault(), Button)
            If btnPlaceOrder IsNot Nothing AndAlso Not btnPlaceOrder.Enabled Then
                btnPlaceOrder.Enabled = True
                btnPlaceOrder.Text = "✅ Buat Pesanan"
                btnPlaceOrder.BackColor = Color.FromArgb(46, 204, 113)
            End If
        End Try
    End Sub

    ' Helper methods untuk loading state
    Private Sub SetLoadingState(isLoading As Boolean)
        Dim btnPlaceOrder As Button = CType(Me.Controls.Find("btnPlaceOrder", True).FirstOrDefault(), Button)
        Dim btnCancel As Button = CType(Me.Controls.Find("btnCancel", True).FirstOrDefault(), Button)

        If btnPlaceOrder IsNot Nothing Then
            If isLoading Then
                btnPlaceOrder.Enabled = False
                btnPlaceOrder.Text = "⏳ Memproses Pesanan..."
                btnPlaceOrder.BackColor = Color.Gray
            Else
                btnPlaceOrder.Enabled = True
                btnPlaceOrder.Text = "✅ Buat Pesanan"
                btnPlaceOrder.BackColor = Color.FromArgb(46, 204, 113)
            End If
        End If

        If btnCancel IsNot Nothing Then
            btnCancel.Enabled = Not isLoading
        End If

        ' Disable semua input controls saat loading
        For Each ctrl As Control In Me.Controls
            If TypeOf ctrl Is TextBox OrElse TypeOf ctrl Is ComboBox Then
                ctrl.Enabled = Not isLoading
            ElseIf TypeOf ctrl Is Panel Then
                For Each subCtrl As Control In ctrl.Controls
                    If TypeOf subCtrl Is TextBox OrElse TypeOf subCtrl Is ComboBox Then
                        subCtrl.Enabled = Not isLoading
                    End If
                Next
            End If
        Next

        Me.Cursor = If(isLoading, Cursors.WaitCursor, Cursors.Default)
    End Sub
End Class
