Imports MySql.Data.MySqlClient
Imports System.IO

Public Class FormWishlist
    Public Property currentUserID As Integer = 1
    Public Property currentUserName As String = "Guest"
    Private wishlistItems As New List(Of Dictionary(Of String, Object))

    Private Sub FormWishlist_Load(sender As Object, e As EventArgs) Handles MyBase.Load
        ' Setup form
        Me.Text = "Wishlist - " & currentUserName
        Me.Size = New Size(1000, 700)
        Me.StartPosition = FormStartPosition.CenterParent
        Me.BackColor = Color.FromArgb(245, 245, 245)

        ' Set form properties untuk tidak bisa di-minimize/maximize
        Me.FormBorderStyle = FormBorderStyle.FixedSingle
        Me.MaximizeBox = False
        Me.MinimizeBox = False
        Me.MinimumSize = New Size(1000, 700)
        Me.MaximumSize = New Size(1000, 700)

        ' Create UI
        CreateUI()

        ' Load wishlist data
        LoadWishlistData()
    End Sub

    Private Sub CreateUI()
        ' Header Panel
        Dim pnlHeader As New Panel With {
            .Size = New Size(<PERSON><PERSON>Width, 80),
            .Location = New Point(0, 0),
            .BackColor = Color.FromArgb(231, 76, 60),
            .Anchor = AnchorStyles.Top Or AnchorStyles.Left Or AnchorStyles.Right
        }

        ' Header gradient effect
        AddHandler pnlHeader.Paint, Sub(sender, e)
                                        Dim rect As Rectangle = pnlHeader.ClientRectangle
                                        Using brush As New Drawing2D.LinearGradientBrush(rect, Color.FromArgb(231, 76, 60), Color.FromArgb(192, 57, 43), Drawing2D.LinearGradientMode.Horizontal)
                                            e.Graphics.FillRectangle(brush, rect)
                                        End Using
                                    End Sub

        ' Header title
        Dim lblTitle As New Label With {
            .Text = "💖 WISHLIST SAYA",
            .Font = New Font("Segoe UI", 24, FontStyle.Bold),
            .ForeColor = Color.White,
            .Location = New Point(30, 20),
            .Size = New Size(400, 40),
            .BackColor = Color.Transparent
        }

        ' Close button
        Dim btnClose As New Button With {
            .Text = "✕",
            .Font = New Font("Segoe UI", 16, FontStyle.Bold),
            .Size = New Size(40, 40),
            .Location = New Point(Me.Width - 60, 20),
            .BackColor = Color.Transparent,
            .ForeColor = Color.White,
            .FlatStyle = FlatStyle.Flat,
            .Cursor = Cursors.Hand,
            .Anchor = AnchorStyles.Top Or AnchorStyles.Right
        }
        btnClose.FlatAppearance.BorderSize = 0
        AddHandler btnClose.Click, Sub() Me.Close()

        pnlHeader.Controls.AddRange({lblTitle, btnClose})

        ' Main Panel with ScrollBar
        Dim pnlMain As New Panel With {
            .Name = "pnlMain",
            .Location = New Point(0, 80),
            .Size = New Size(Me.Width, Me.Height - 80),
            .BackColor = Color.FromArgb(245, 245, 245),
            .AutoScroll = True,
            .Anchor = AnchorStyles.Top Or AnchorStyles.Left Or AnchorStyles.Right Or AnchorStyles.Bottom
        }

        ' Info Panel
        Dim pnlInfo As New Panel With {
            .Name = "pnlInfo",
            .Size = New Size(Me.Width - 40, 60),
            .Location = New Point(20, 20),
            .BackColor = Color.White,
            .Anchor = AnchorStyles.Top Or AnchorStyles.Left Or AnchorStyles.Right
        }

        ' Info label
        Dim lblInfo As New Label With {
            .Name = "lblInfo",
            .Text = "Loading wishlist...",
            .Font = New Font("Segoe UI", 12),
            .ForeColor = Color.FromArgb(52, 73, 94),
            .Location = New Point(20, 20),
            .Size = New Size(400, 25),
            .BackColor = Color.Transparent
        }

        pnlInfo.Controls.Add(lblInfo)

        ' Products Panel
        Dim pnlProducts As New Panel With {
            .Name = "pnlProducts",
            .Location = New Point(20, 100),
            .Size = New Size(Me.Width - 60, 500),
            .BackColor = Color.Transparent,
            .Anchor = AnchorStyles.Top Or AnchorStyles.Left Or AnchorStyles.Right Or AnchorStyles.Bottom
        }

        pnlMain.Controls.AddRange({pnlInfo, pnlProducts})

        ' Add controls to form
        Me.Controls.Add(pnlMain)
        Me.Controls.Add(pnlHeader)
    End Sub

    Private Sub LoadWishlistData()
        Try
            koneksi()

            Dim query As String = "SELECT p.product_id, p.product_name, c.category_name, p.price, p.stock, p.description, p.image_url, " &
                                 "COALESCE(w.wishlist_count, 0) as wishlist_count, COALESCE(s.sold_count, 0) as sold_count " &
                                 "FROM products p " &
                                 "JOIN categories c ON p.category_id = c.category_id " &
                                 "JOIN wishlist wl ON p.product_id = wl.product_id " &
                                 "LEFT JOIN (SELECT product_id, COUNT(*) as wishlist_count FROM wishlist GROUP BY product_id) w ON p.product_id = w.product_id " &
                                 "LEFT JOIN (SELECT product_id, COUNT(*) as sold_count FROM order_details GROUP BY product_id) s ON p.product_id = s.product_id " &
                                 "WHERE wl.user_id = @userID " &
                                 "ORDER BY wl.created_at DESC"

            cmd = New MySqlCommand(query, conn)
            cmd.Parameters.AddWithValue("@userID", currentUserID)
            dr = cmd.ExecuteReader()

            wishlistItems.Clear()
            While dr.Read()
                Dim product As New Dictionary(Of String, Object) From {
                    {"product_id", dr("product_id")},
                    {"product_name", dr("product_name").ToString()},
                    {"category_name", dr("category_name").ToString()},
                    {"price", Convert.ToDecimal(dr("price"))},
                    {"stock", Convert.ToInt32(dr("stock"))},
                    {"description", dr("description").ToString()},
                    {"image_url", If(IsDBNull(dr("image_url")), "", dr("image_url").ToString())},
                    {"wishlist_count", Convert.ToInt32(dr("wishlist_count"))},
                    {"sold_count", Convert.ToInt32(dr("sold_count"))}
                }
                wishlistItems.Add(product)
            End While

            dr.Close()
            tutupKoneksi()

            ' Update UI
            UpdateWishlistDisplay()

        Catch ex As Exception
            MessageBox.Show("Error loading wishlist: " & ex.Message, "Error", MessageBoxButtons.OK, MessageBoxIcon.Error)
        End Try
    End Sub

    Private Sub UpdateWishlistDisplay()
        Dim pnlProducts As Panel = CType(Me.Controls.Find("pnlProducts", True).FirstOrDefault(), Panel)
        Dim lblInfo As Label = CType(Me.Controls.Find("lblInfo", True).FirstOrDefault(), Label)

        If pnlProducts Is Nothing OrElse lblInfo Is Nothing Then Return

        ' Clear existing products
        pnlProducts.Controls.Clear()

        ' Update info
        lblInfo.Text = $"📋 Anda memiliki {wishlistItems.Count} produk di wishlist"

        If wishlistItems.Count = 0 Then
            ' Show empty state
            Dim lblEmpty As New Label With {
                .Text = "💔 Wishlist Anda kosong" & vbCrLf & "Tambahkan produk favorit dengan klik tombol ♡ di product card",
                .Font = New Font("Segoe UI", 14),
                .ForeColor = Color.FromArgb(149, 165, 166),
                .Location = New Point(50, 100),
                .Size = New Size(400, 100),
                .TextAlign = ContentAlignment.MiddleCenter,
                .BackColor = Color.Transparent
            }
            pnlProducts.Controls.Add(lblEmpty)
            Return
        End If

        ' Display products in grid - layout yang lebih rapi
        Dim x As Integer = 0
        Dim y As Integer = 0
        Dim cardWidth As Integer = 320
        Dim cardHeight As Integer = 450
        Dim margin As Integer = 25
        Dim cardsPerRow As Integer = Math.Floor((pnlProducts.Width - margin) / (cardWidth + margin))

        For i As Integer = 0 To wishlistItems.Count - 1
            Dim product = wishlistItems(i)
            Dim card = CreateWishlistCard(product)

            card.Location = New Point(x * (cardWidth + margin) + margin, y * (cardHeight + margin) + margin)
            pnlProducts.Controls.Add(card)

            x += 1
            If x >= cardsPerRow Then
                x = 0
                y += 1
            End If
        Next

        ' Update panel height for scrolling dengan padding tambahan
        Dim totalRows As Integer = Math.Ceiling(wishlistItems.Count / cardsPerRow)
        pnlProducts.Height = Math.Max(500, totalRows * (cardHeight + margin) + margin + 50)
    End Sub

    Private Function CreateWishlistCard(product As Dictionary(Of String, Object)) As Panel
        Dim card As New Panel With {
            .Size = New Size(320, 450),
            .BackColor = Color.White,
            .Margin = New Padding(10),
            .Cursor = Cursors.Hand
        }

        ' Card shadow effect
        AddHandler card.Paint, Sub(sender, e)
                                   Dim rect As Rectangle = New Rectangle(3, 3, card.Width - 6, card.Height - 6)
                                   Using brush As New SolidBrush(Color.FromArgb(30, 0, 0, 0))
                                       e.Graphics.FillRectangle(brush, rect)
                                   End Using
                               End Sub

        ' Image panel
        Dim pnlImage As New Panel With {
            .Size = New Size(320, 220),
            .Location = New Point(0, 0),
            .BackColor = Color.FromArgb(236, 240, 241)
        }

        ' Product image
        Dim picProduct As New PictureBox With {
            .Size = New Size(320, 220),
            .Location = New Point(0, 0),
            .SizeMode = PictureBoxSizeMode.Zoom,
            .BackColor = Color.FromArgb(236, 240, 241)
        }

        ' Load image
        If product("image_url").ToString() <> "" AndAlso File.Exists(product("image_url").ToString()) Then
            Try
                picProduct.Image = Image.FromFile(product("image_url").ToString())
            Catch
                ShowImagePlaceholder(picProduct)
            End Try
        Else
            ShowImagePlaceholder(picProduct)
        End If

        pnlImage.Controls.Add(picProduct)

        ' Remove from wishlist button
        Dim btnRemove As New Label With {
            .Text = "🗑️",
            .Font = New Font("Segoe UI", 16, FontStyle.Bold),
            .Size = New Size(40, 40),
            .Location = New Point(270, 10),
            .BackColor = Color.White,
            .ForeColor = Color.FromArgb(231, 76, 60),
            .TextAlign = ContentAlignment.MiddleCenter,
            .Cursor = Cursors.Hand,
            .Tag = product("product_id"),
            .BorderStyle = BorderStyle.FixedSingle
        }

        AddHandler btnRemove.Click, AddressOf BtnRemoveWishlist_Click
        pnlImage.Controls.Add(btnRemove)

        ' Product info panel
        Dim pnlInfo As New Panel With {
            .Size = New Size(300, 220),
            .Location = New Point(10, 230),
            .BackColor = Color.Transparent
        }

        ' Product name
        Dim lblName As New Label With {
            .Text = product("product_name").ToString(),
            .Font = New Font("Segoe UI", 12, FontStyle.Bold),
            .Location = New Point(0, 5),
            .Size = New Size(300, 30),
            .ForeColor = Color.FromArgb(44, 62, 80),
            .BackColor = Color.Transparent
        }

        ' Category
        Dim lblCategory As New Label With {
            .Text = product("category_name").ToString(),
            .Font = New Font("Segoe UI", 10),
            .Location = New Point(0, 35),
            .Size = New Size(190, 20),
            .ForeColor = Color.FromArgb(149, 165, 166),
            .BackColor = Color.Transparent
        }

        ' Price
        Dim lblPrice As New Label With {
            .Text = "Rp " & String.Format("{0:N0}", product("price")),
            .Font = New Font("Segoe UI", 14, FontStyle.Bold),
            .Location = New Point(0, 60),
            .Size = New Size(250, 25),
            .ForeColor = Color.FromArgb(231, 76, 60),
            .BackColor = Color.Transparent
        }

        ' Stock
        Dim lblStock As New Label With {
            .Text = If(Convert.ToInt32(product("stock")) > 0, $"Stok: {product("stock")}", "Stok Habis"),
            .Font = New Font("Segoe UI", 10),
            .Location = New Point(0, 90),
            .Size = New Size(120, 20),
            .ForeColor = If(Convert.ToInt32(product("stock")) > 0, Color.FromArgb(46, 204, 113), Color.FromArgb(231, 76, 60)),
            .BackColor = Color.Transparent
        }

        ' Add to cart button - posisi rapi
        Dim btnAddCart As New Button With {
            .Text = If(Convert.ToInt32(product("stock")) > 0, "TAMBAH KE KERANJANG", "Stok Habis"),
            .Tag = product,
            .Location = New Point(0, 150),
            .Size = New Size(145, 40),
            .BackColor = If(Convert.ToInt32(product("stock")) > 0, Color.FromArgb(52, 152, 219), Color.Gray),
            .ForeColor = Color.White,
            .FlatStyle = FlatStyle.Flat,
            .Font = New Font("Segoe UI", 9, FontStyle.Bold),
            .Enabled = Convert.ToInt32(product("stock")) > 0,
            .Cursor = Cursors.Hand,
            .TextAlign = ContentAlignment.MiddleCenter
        }
        btnAddCart.FlatAppearance.BorderSize = 0
        AddHandler btnAddCart.Click, AddressOf BtnAddToCart_Click

        ' View details button - posisi rapi
        Dim btnDetails As New Button With {
            .Text = "LIHAT DETAIL",
            .Tag = product,
            .Location = New Point(155, 150),
            .Size = New Size(145, 40),
            .BackColor = Color.FromArgb(155, 89, 182),
            .ForeColor = Color.White,
            .FlatStyle = FlatStyle.Flat,
            .Font = New Font("Segoe UI", 9, FontStyle.Bold),
            .Cursor = Cursors.Hand,
            .TextAlign = ContentAlignment.MiddleCenter
        }
        btnDetails.FlatAppearance.BorderSize = 0
        AddHandler btnDetails.Click, AddressOf BtnViewDetails_Click

        pnlInfo.Controls.AddRange({lblName, lblCategory, lblPrice, lblStock, btnAddCart, btnDetails})
        card.Controls.AddRange({pnlImage, pnlInfo})

        Return card
    End Function

    Private Sub ShowImagePlaceholder(picBox As PictureBox)
        Dim lblPlaceholder As New Label With {
            .Text = "IMG",
            .Font = New Font("Segoe UI", 12, FontStyle.Bold),
            .ForeColor = Color.FromArgb(189, 195, 199),
            .Dock = DockStyle.Fill,
            .TextAlign = ContentAlignment.MiddleCenter,
            .BackColor = Color.Transparent
        }
        picBox.Controls.Add(lblPlaceholder)
    End Sub

    Private Sub BtnRemoveWishlist_Click(sender As Object, e As EventArgs)
        Dim lbl As Label = CType(sender, Label)
        Dim productID As Integer = Convert.ToInt32(lbl.Tag)

        Dim result = MessageBox.Show("Hapus produk ini dari wishlist?", "Konfirmasi", MessageBoxButtons.YesNo, MessageBoxIcon.Question)
        If result = DialogResult.Yes Then
            Try
                koneksi()
                cmd = New MySqlCommand("DELETE FROM wishlist WHERE user_id = @userID AND product_id = @productID", conn)
                cmd.Parameters.AddWithValue("@userID", currentUserID)
                cmd.Parameters.AddWithValue("@productID", productID)
                cmd.ExecuteNonQuery()
                tutupKoneksi()

                ' Reload data
                LoadWishlistData()
                MessageBox.Show("Produk berhasil dihapus dari wishlist!", "Sukses", MessageBoxButtons.OK, MessageBoxIcon.Information)

            Catch ex As Exception
                MessageBox.Show("Error removing from wishlist: " & ex.Message, "Error", MessageBoxButtons.OK, MessageBoxIcon.Error)
            End Try
        End If
    End Sub

    Private Sub BtnAddToCart_Click(sender As Object, e As EventArgs)
        Dim btn As Button = CType(sender, Button)
        Dim product As Dictionary(Of String, Object) = CType(btn.Tag, Dictionary(Of String, Object))

        ' Show add to cart confirmation
        MessageBox.Show($"Produk '{product("product_name")}' berhasil ditambahkan ke keranjang!", "Sukses", MessageBoxButtons.OK, MessageBoxIcon.Information)
    End Sub

    Private Sub BtnViewDetails_Click(sender As Object, e As EventArgs)
        Dim btn As Button = CType(sender, Button)
        Dim product As Dictionary(Of String, Object) = CType(btn.Tag, Dictionary(Of String, Object))

        ' Show product details
        Dim details As String = $"Nama: {product("product_name")}" & vbCrLf &
                               $"Kategori: {product("category_name")}" & vbCrLf &
                               $"Harga: Rp {String.Format("{0:N0}", product("price"))}" & vbCrLf &
                               $"Stok: {product("stock")}" & vbCrLf &
                               $"Deskripsi: {product("description")}"

        MessageBox.Show(details, "Detail Produk", MessageBoxButtons.OK, MessageBoxIcon.Information)
    End Sub
End Class
