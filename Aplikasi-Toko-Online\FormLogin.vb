﻿' FormLogin.vb - Compatible dengan Designer
Imports MySql.Data.MySqlClient
Imports System.Security.Cryptography
Imports System.Text

Public Class FormLogin
    Private Sub FormLogin_Load(sender As Object, e As EventArgs) Handles MyBase.Load
        ' Setup form properties
        Me.Text = "Login"
        Me.Size = New Size(400, 420)
        Me.StartPosition = FormStartPosition.CenterScreen
        Me.FormBorderStyle = FormBorderStyle.None
        Me.BackColor = Color.White

        ' Create UI controls
        SetupLoginUI()
    End Sub

    Private Sub SetupLoginUI()
        ' Clear existing controls first
        Me.Controls.Clear()

        ' Panel Header
        Dim pnlHeader As New Panel With {
            .Dock = DockStyle.Top,
            .Height = 120,
            .BackColor = Color.FromArgb(41, 128, 185)
        }

        ' Logo/Title
        Dim lblLogo As New Label With {
            .Text = "TOKO ONLINE",
            .Font = New Font("Segoe UI", 24, FontStyle.Bold),
            .ForeColor = Color.White,
            .AutoSize = True,
            .Location = New Point(100, 30),
            .TextAlign = ContentAlignment.MiddleCenter
        }

        Dim lblSubtitle As New Label With {
            .Text = "Sistem Penjualan Online",
            .Font = New Font("Segoe UI", 12),
            .ForeColor = Color.White,
            .AutoSize = True,
            .Location = New Point(130, 70)
        }

        pnlHeader.Controls.AddRange({lblLogo, lblSubtitle})

        ' Close button
        Dim btnClose As New Label With {
            .Text = "X",
            .Font = New Font("Segoe UI", 12, FontStyle.Bold),
            .ForeColor = Color.White,
            .Size = New Size(30, 30),
            .Location = New Point(360, 10),
            .TextAlign = ContentAlignment.MiddleCenter,
            .Cursor = Cursors.Hand
        }
        AddHandler btnClose.Click, Sub() Application.Exit()
        AddHandler btnClose.MouseEnter, Sub() btnClose.BackColor = Color.FromArgb(192, 57, 43)
        AddHandler btnClose.MouseLeave, Sub() btnClose.BackColor = Color.Transparent
        pnlHeader.Controls.Add(btnClose)

        ' Panel Login
        Dim pnlLogin As New Panel With {
            .Size = New Size(300, 230),
            .Location = New Point(50, 150),
            .BackColor = Color.White
        }

        ' Username
        Dim lblUsername As New Label With {
            .Text = "Username",
            .Font = New Font("Segoe UI", 10),
            .ForeColor = Color.FromArgb(52, 73, 94),
            .Location = New Point(0, 0),
            .AutoSize = True
        }

        Dim txtUsername As New TextBox With {
            .Name = "txtUsername",
            .Size = New Size(300, 35),
            .Location = New Point(0, 25),
            .Font = New Font("Segoe UI", 12),
            .BorderStyle = BorderStyle.FixedSingle
        }

        ' Password
        Dim lblPassword As New Label With {
            .Text = "Password",
            .Font = New Font("Segoe UI", 10),
            .ForeColor = Color.FromArgb(52, 73, 94),
            .Location = New Point(0, 70),
            .AutoSize = True
        }

        Dim txtPassword As New TextBox With {
            .Name = "txtPassword",
            .Size = New Size(300, 35),
            .Location = New Point(0, 95),
            .Font = New Font("Segoe UI", 12),
            .BorderStyle = BorderStyle.FixedSingle,
            .UseSystemPasswordChar = True
        }

        ' Show password checkbox
        Dim chkShowPassword As New CheckBox With {
            .Text = "Tampilkan password",
            .Location = New Point(0, 135),
            .AutoSize = True,
            .Font = New Font("Segoe UI", 9)
        }
        AddHandler chkShowPassword.CheckedChanged, Sub()
                                                       txtPassword.UseSystemPasswordChar = Not chkShowPassword.Checked
                                                   End Sub

        ' Login button
        Dim btnLogin As New Button With {
            .Text = "LOGIN",
            .Size = New Size(300, 45),
            .Location = New Point(0, 170),
            .BackColor = Color.FromArgb(46, 204, 113),
            .ForeColor = Color.White,
            .FlatStyle = FlatStyle.Flat,
            .Font = New Font("Segoe UI", 11, FontStyle.Bold),
            .Cursor = Cursors.Hand
        }
        btnLogin.FlatAppearance.BorderSize = 0
        AddHandler btnLogin.Click, AddressOf BtnLogin_Click

        pnlLogin.Controls.AddRange({lblUsername, txtUsername, lblPassword, txtPassword, chkShowPassword, btnLogin})

        Me.Controls.AddRange({pnlHeader, pnlLogin})
    End Sub

    Private Sub BtnLogin_Click(sender As Object, e As EventArgs)
        Dim txtUsername As TextBox = CType(Me.Controls.Find("txtUsername", True).FirstOrDefault(), TextBox)
        Dim txtPassword As TextBox = CType(Me.Controls.Find("txtPassword", True).FirstOrDefault(), TextBox)

        If txtUsername Is Nothing OrElse txtPassword Is Nothing Then
            MessageBox.Show("Error: Controls not found!", "Error", MessageBoxButtons.OK, MessageBoxIcon.Error)
            Return
        End If

        Dim username As String = txtUsername.Text.Trim()
        Dim password As String = txtPassword.Text

        If username = "" Or password = "" Then
            MessageBox.Show("Username dan password harus diisi!", "Peringatan", MessageBoxButtons.OK, MessageBoxIcon.Warning)
            Return
        End If

        Try
            koneksi()

            ' Query untuk login
            cmd = New MySqlCommand("SELECT * FROM users WHERE username = @username AND password = @password", conn)
            cmd.Parameters.AddWithValue("@username", username)
            cmd.Parameters.AddWithValue("@password", password)

            dr = cmd.ExecuteReader()

            If dr.Read() Then
                ' Login berhasil
                Dim userID As Integer = Convert.ToInt32(dr("user_id"))
                Dim fullName As String = dr("full_name").ToString()

                ' Check if role column exists and get role
                Dim userRole As String = "user" ' Default role
                Dim isAdmin As Boolean = False

                Try
                    ' Try to get role from database
                    userRole = dr("role").ToString()
                    isAdmin = (userRole.ToLower() = "admin")
                Catch ex As Exception
                    ' If role column doesn't exist, fallback to username check
                    Dim dbUsername As String = dr("username").ToString()
                    isAdmin = (dbUsername.ToLower() = "admin")
                    userRole = If(isAdmin, "admin", "user")
                End Try

                dr.Close()
                tutupKoneksi()

                MessageBox.Show($"Selamat datang, {fullName}!", "Login Berhasil", MessageBoxButtons.OK, MessageBoxIcon.Information)

                ' Redirect based on role
                If isAdmin Then
                    ' Admin langsung ke Admin Dashboard
                    Dim formAdminDashboard As New FormAdminDashboard()
                    formAdminDashboard.currentUserID = userID
                    formAdminDashboard.currentUserName = fullName
                    formAdminDashboard.Show()
                    Me.Hide()
                Else
                    ' User biasa ke FormMain
                    Dim formMain As New FormMain()
                    formMain.currentUserID = userID
                    formMain.currentUserName = fullName
                    formMain.isAdmin = isAdmin
                    formMain.Show()
                    Me.Hide()
                End If
            Else
                dr.Close()
                tutupKoneksi()
                MessageBox.Show("Username atau password salah!", "Login Gagal", MessageBoxButtons.OK, MessageBoxIcon.Error)
            End If

        Catch ex As Exception
            MessageBox.Show("Error: " & ex.Message, "Error", MessageBoxButtons.OK, MessageBoxIcon.Error)
            If dr IsNot Nothing AndAlso Not dr.IsClosed Then dr.Close()
            tutupKoneksi()
        End Try
    End Sub



    Private Function GetMD5Hash(input As String) As String
        Using md5 As MD5 = MD5.Create()
            Dim inputBytes As Byte() = Encoding.ASCII.GetBytes(input)
            Dim hashBytes As Byte() = md5.ComputeHash(inputBytes)

            Dim sb As New StringBuilder()
            For Each b As Byte In hashBytes
                sb.Append(b.ToString("X2"))
            Next
            Return sb.ToString()
        End Using
    End Function
End Class