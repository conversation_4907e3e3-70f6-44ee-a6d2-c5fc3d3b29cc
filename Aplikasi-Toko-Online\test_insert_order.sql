-- <PERSON>ript untuk test insert order secara manual
USE db_ecommerce;

-- Test insert ke tabel orders
INSERT INTO orders (user_id, order_date, total_amount, order_status, shipping_address, payment_method) 
VALUES (1, NOW(), 100000, 'pending', 'Test Address', 'Transfer Bank');

-- Cek apakah berhasil
SELECT * FROM orders ORDER BY order_id DESC LIMIT 1;

-- Test insert ke tabel order_details (ganti order_id dan product_id sesuai data yang ada)
INSERT INTO order_details (order_id, product_id, quantity, price, subtotal) 
VALUES (LAST_INSERT_ID(), 1, 2, 50000, 100000);

-- Cek apakah berhasil
SELECT * FROM order_details ORDER BY order_detail_id DESC LIMIT 1;

-- Hapus data test
DELETE FROM order_details WHERE order_id = LAST_INSERT_ID();
DELETE FROM orders WHERE order_id = LAST_INSERT_ID();
