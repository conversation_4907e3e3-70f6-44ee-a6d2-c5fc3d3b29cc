﻿' FormEditProduct.vb - Form untuk Edit Produk
Imports MySql.Data.MySqlClient
Imports System.IO

Public Class FormEditProduct
    Private productID As Integer
    Private currentImagePath As String = ""
    Private originalImagePath As String = ""

    Public Sub New(prodID As Integer)
        InitializeComponent()
        productID = prodID
    End Sub

    Private Sub FormEditProduct_Load(sender As Object, e As EventArgs) Handles MyBase.Load
        Me.Text = "Edit Produk"
        Me.Size = New Size(500, 700)
        Me.StartPosition = FormStartPosition.CenterScreen
        Me.FormBorderStyle = FormBorderStyle.FixedDialog
        Me.MaximizeBox = False
        Me.BackColor = Color.White

        CreateUI()
        LoadCategories()
        LoadProductData()
    End Sub

    Private Sub CreateUI()
        ' Header Panel
        Dim pnlHeader As New Panel With {
            .Dock = DockStyle.Top,
            .Height = 60,
            .BackColor = Color.FromArgb(52, 152, 219)
        }

        Dim lblTitle As New Label With {
            .Text = "EDIT PRODUK",
            .Font = New Font("Segoe UI", 16, FontStyle.Bold),
            .ForeColor = Color.White,
            .Location = New Point(20, 15),
            .AutoSize = True
        }

        pnlHeader.Controls.Add(lblTitle)

        ' Main Panel - Same layout as Add form
        Dim pnlMain As New Panel With {
            .Dock = DockStyle.Fill,
            .Padding = New Padding(30)
        }

        Dim yPos As Integer = 20

        ' Product ID (Hidden)
        Dim txtProductID As New TextBox With {
            .Name = "txtProductID",
            .Text = productID.ToString(),
            .Visible = False
        }

        ' Product Name
        Dim lblName As New Label With {
            .Text = "Nama Produk *",
            .Location = New Point(30, yPos),
            .AutoSize = True,
            .Font = New Font("Segoe UI", 10)
        }
        yPos += 25

        Dim txtName As New TextBox With {
            .Name = "txtProductName",
            .Size = New Size(400, 30),
            .Location = New Point(30, yPos),
            .Font = New Font("Segoe UI", 11)
        }
        yPos += 40

        ' Category
        Dim lblCategory As New Label With {
            .Text = "Kategori *",
            .Location = New Point(30, yPos),
            .AutoSize = True,
            .Font = New Font("Segoe UI", 10)
        }
        yPos += 25

        Dim cmbCategory As New ComboBox With {
            .Name = "cmbCategory",
            .Size = New Size(400, 30),
            .Location = New Point(30, yPos),
            .DropDownStyle = ComboBoxStyle.DropDownList,
            .Font = New Font("Segoe UI", 11)
        }
        yPos += 40

        ' Price
        Dim lblPrice As New Label With {
            .Text = "Harga (Rp) *",
            .Location = New Point(30, yPos),
            .AutoSize = True,
            .Font = New Font("Segoe UI", 10)
        }
        yPos += 25

        Dim txtPrice As New TextBox With {
            .Name = "txtPrice",
            .Size = New Size(400, 30),
            .Location = New Point(30, yPos),
            .Font = New Font("Segoe UI", 11)
        }
        yPos += 40

        ' Stock
        Dim lblStock As New Label With {
            .Text = "Stok *",
            .Location = New Point(30, yPos),
            .AutoSize = True,
            .Font = New Font("Segoe UI", 10)
        }
        yPos += 25

        Dim txtStock As New TextBox With {
            .Name = "txtStock",
            .Size = New Size(400, 30),
            .Location = New Point(30, yPos),
            .Font = New Font("Segoe UI", 11)
        }
        yPos += 40

        ' Description
        Dim lblDesc As New Label With {
            .Text = "Deskripsi",
            .Location = New Point(30, yPos),
            .AutoSize = True,
            .Font = New Font("Segoe UI", 10)
        }
        yPos += 25

        Dim txtDesc As New TextBox With {
            .Name = "txtDescription",
            .Size = New Size(400, 80),
            .Location = New Point(30, yPos),
            .Multiline = True,
            .Font = New Font("Segoe UI", 10),
            .ScrollBars = ScrollBars.Vertical
        }
        yPos += 90

        ' Image Section
        Dim lblImage As New Label With {
            .Text = "Gambar Produk",
            .Location = New Point(30, yPos),
            .AutoSize = True,
            .Font = New Font("Segoe UI", 10)
        }
        yPos += 25

        ' Image Preview
        Dim picProduct As New PictureBox With {
            .Name = "picProduct",
            .Size = New Size(120, 120),
            .Location = New Point(30, yPos),
            .BackColor = Color.FromArgb(236, 240, 241),
            .SizeMode = PictureBoxSizeMode.Zoom,
            .BorderStyle = BorderStyle.FixedSingle
        }

        ' Browse Button
        Dim btnBrowse As New Button With {
            .Text = "Ganti Gambar",
            .Size = New Size(120, 35),
            .Location = New Point(160, yPos),
            .BackColor = Color.FromArgb(52, 152, 219),
            .ForeColor = Color.White,
            .FlatStyle = FlatStyle.Flat,
            .Font = New Font("Segoe UI", 9),
            .Cursor = Cursors.Hand
        }
        btnBrowse.FlatAppearance.BorderSize = 0
        AddHandler btnBrowse.Click, AddressOf BtnBrowse_Click

        ' Remove Image Button
        Dim btnRemoveImage As New Button With {
            .Text = "Hapus Gambar",
            .Size = New Size(120, 35),
            .Location = New Point(290, yPos),
            .BackColor = Color.FromArgb(149, 165, 166),
            .ForeColor = Color.White,
            .FlatStyle = FlatStyle.Flat,
            .Font = New Font("Segoe UI", 9),
            .Cursor = Cursors.Hand
        }
        btnRemoveImage.FlatAppearance.BorderSize = 0
        AddHandler btnRemoveImage.Click, Sub()
                                             currentImagePath = ""
                                             picProduct.Image = Nothing
                                             Dim lblPlaceholder As New Label With {
                                                 .Text = "IMG",
                                                 .Font = New Font("Segoe UI", 16, FontStyle.Bold),
                                                 .ForeColor = Color.FromArgb(189, 195, 199),
                                                 .Dock = DockStyle.Fill,
                                                 .TextAlign = ContentAlignment.MiddleCenter
                                             }
                                             picProduct.Controls.Clear()
                                             picProduct.Controls.Add(lblPlaceholder)
                                         End Sub

        yPos += 130

        ' Buttons
        Dim btnUpdate As New Button With {
            .Text = "UPDATE",
            .Size = New Size(190, 45),
            .Location = New Point(30, yPos),
            .BackColor = Color.FromArgb(52, 152, 219),
            .ForeColor = Color.White,
            .FlatStyle = FlatStyle.Flat,
            .Font = New Font("Segoe UI", 11, FontStyle.Bold),
            .Cursor = Cursors.Hand
        }
        btnUpdate.FlatAppearance.BorderSize = 0
        AddHandler btnUpdate.Click, AddressOf BtnUpdate_Click

        Dim btnCancel As New Button With {
            .Text = "BATAL",
            .Size = New Size(190, 45),
            .Location = New Point(240, yPos),
            .BackColor = Color.FromArgb(149, 165, 166),
            .ForeColor = Color.White,
            .FlatStyle = FlatStyle.Flat,
            .Font = New Font("Segoe UI", 11, FontStyle.Bold),
            .Cursor = Cursors.Hand
        }
        btnCancel.FlatAppearance.BorderSize = 0
        AddHandler btnCancel.Click, Sub() Me.Close()

        pnlMain.Controls.AddRange({txtProductID, lblName, txtName, lblCategory, cmbCategory,
                                  lblPrice, txtPrice, lblStock, txtStock,
                                  lblDesc, txtDesc, lblImage, picProduct,
                                  btnBrowse, btnRemoveImage, btnUpdate, btnCancel})

        Me.Controls.AddRange({pnlMain, pnlHeader})
    End Sub

    Private Sub LoadCategories()
        Try
            koneksi()

            cmd = New MySqlCommand("SELECT category_id, category_name FROM categories ORDER BY category_name", conn)
            dr = cmd.ExecuteReader()

            Dim cmb As ComboBox = CType(Me.Controls.Find("cmbCategory", True).FirstOrDefault(), ComboBox)
            cmb.Items.Clear()

            Dim categories As New Dictionary(Of String, Integer)

            While dr.Read()
                categories.Add(dr("category_name").ToString(), Convert.ToInt32(dr("category_id")))
            End While

            dr.Close()
            tutupKoneksi()

            cmb.DataSource = New BindingSource(categories, Nothing)
            cmb.DisplayMember = "Key"
            cmb.ValueMember = "Value"

        Catch ex As Exception
            MessageBox.Show("Error loading categories: " & ex.Message, "Error", MessageBoxButtons.OK, MessageBoxIcon.Error)
        End Try
    End Sub

    Private Sub LoadProductData()
        Try
            koneksi()

            cmd = New MySqlCommand("SELECT p.*, c.category_name FROM products p " &
                                  "JOIN categories c ON p.category_id = c.category_id " &
                                  "WHERE p.product_id = @id", conn)
            cmd.Parameters.AddWithValue("@id", productID)
            dr = cmd.ExecuteReader()

            If dr.Read() Then
                CType(Me.Controls.Find("txtProductName", True).FirstOrDefault(), TextBox).Text = dr("product_name").ToString()
                CType(Me.Controls.Find("cmbCategory", True).FirstOrDefault(), ComboBox).Text = dr("category_name").ToString()
                CType(Me.Controls.Find("txtPrice", True).FirstOrDefault(), TextBox).Text = dr("price").ToString()
                CType(Me.Controls.Find("txtStock", True).FirstOrDefault(), TextBox).Text = dr("stock").ToString()
                CType(Me.Controls.Find("txtDescription", True).FirstOrDefault(), TextBox).Text = dr("description").ToString()

                ' Load image
                originalImagePath = dr("image_url").ToString()
                currentImagePath = originalImagePath

                If originalImagePath <> "" AndAlso File.Exists(originalImagePath) Then
                    Dim picProduct As PictureBox = CType(Me.Controls.Find("picProduct", True).FirstOrDefault(), PictureBox)
                    Try
                        picProduct.Image = Image.FromFile(originalImagePath)
                    Catch
                        ' Show placeholder if image load fails
                        ShowImagePlaceholder(picProduct)
                    End Try
                Else
                    ShowImagePlaceholder(CType(Me.Controls.Find("picProduct", True).FirstOrDefault(), PictureBox))
                End If
            End If

            dr.Close()
            tutupKoneksi()

        Catch ex As Exception
            MessageBox.Show("Error loading product data: " & ex.Message, "Error", MessageBoxButtons.OK, MessageBoxIcon.Error)
        End Try
    End Sub

    Private Sub ShowImagePlaceholder(picBox As PictureBox)
        Dim lblPlaceholder As New Label With {
            .Text = "IMG",
            .Font = New Font("Segoe UI", 16, FontStyle.Bold),
            .ForeColor = Color.FromArgb(189, 195, 199),
            .Dock = DockStyle.Fill,
            .TextAlign = ContentAlignment.MiddleCenter
        }
        picBox.Controls.Clear()
        picBox.Controls.Add(lblPlaceholder)
    End Sub

    Private Sub BtnBrowse_Click(sender As Object, e As EventArgs)
        Dim openFileDialog As New OpenFileDialog With {
            .Filter = "Image Files|*.jpg;*.jpeg;*.png;*.gif;*.bmp",
            .Title = "Pilih Gambar Produk"
        }

        If openFileDialog.ShowDialog() = DialogResult.OK Then
            currentImagePath = openFileDialog.FileName

            Dim picProduct As PictureBox = CType(Me.Controls.Find("picProduct", True).FirstOrDefault(), PictureBox)
            Try
                picProduct.Image = Image.FromFile(currentImagePath)
                picProduct.Controls.Clear()
            Catch ex As Exception
                MessageBox.Show("Error loading image: " & ex.Message, "Error", MessageBoxButtons.OK, MessageBoxIcon.Error)
            End Try
        End If
    End Sub

    Private Sub BtnUpdate_Click(sender As Object, e As EventArgs)
        If Not ValidateInput() Then Return

        Try
            koneksi()

            ' Handle image
            Dim imagePath As String = originalImagePath
            If currentImagePath <> originalImagePath AndAlso currentImagePath <> "" Then
                ' New image selected
                imagePath = SaveProductImage()
            End If

            Dim categoryID As Integer = CType(CType(Me.Controls.Find("cmbCategory", True).FirstOrDefault(), ComboBox).SelectedValue, Integer)

            cmd = New MySqlCommand("UPDATE products SET product_name = @name, category_id = @category, " &
                                  "price = @price, stock = @stock, description = @desc, image_url = @image " &
                                  "WHERE product_id = @id", conn)

            cmd.Parameters.AddWithValue("@id", productID)
            cmd.Parameters.AddWithValue("@name", CType(Me.Controls.Find("txtProductName", True).FirstOrDefault(), TextBox).Text.Trim())
            cmd.Parameters.AddWithValue("@category", categoryID)
            cmd.Parameters.AddWithValue("@price", Convert.ToDecimal(CType(Me.Controls.Find("txtPrice", True).FirstOrDefault(), TextBox).Text))
            cmd.Parameters.AddWithValue("@stock", Convert.ToInt32(CType(Me.Controls.Find("txtStock", True).FirstOrDefault(), TextBox).Text))
            cmd.Parameters.AddWithValue("@desc", CType(Me.Controls.Find("txtDescription", True).FirstOrDefault(), TextBox).Text.Trim())
            cmd.Parameters.AddWithValue("@image", imagePath)

            cmd.ExecuteNonQuery()
            tutupKoneksi()

            MessageBox.Show("Produk berhasil diupdate!", "Sukses", MessageBoxButtons.OK, MessageBoxIcon.Information)

            Me.DialogResult = DialogResult.OK
            Me.Close()

        Catch ex As Exception
            MessageBox.Show("Error: " & ex.Message, "Error", MessageBoxButtons.OK, MessageBoxIcon.Error)
        End Try
    End Sub

    Private Function SaveProductImage() As String
        If currentImagePath = "" OrElse Not File.Exists(currentImagePath) Then
            Return ""
        End If

        Try
            Dim imagesFolder As String = Path.Combine(Application.StartupPath, "Images", "Products")
            If Not Directory.Exists(imagesFolder) Then
                Directory.CreateDirectory(imagesFolder)
            End If

            Dim extension As String = Path.GetExtension(currentImagePath)
            Dim newFileName As String = $"product_{productID}_{DateTime.Now:yyyyMMddHHmmss}{extension}"
            Dim newPath As String = Path.Combine(imagesFolder, newFileName)

            File.Copy(currentImagePath, newPath, True)

            ' Delete old image if different
            If originalImagePath <> "" AndAlso File.Exists(originalImagePath) AndAlso originalImagePath <> newPath Then
                Try
                    File.Delete(originalImagePath)
                Catch
                    ' Ignore if cannot delete
                End Try
            End If

            Return newPath

        Catch ex As Exception
            MessageBox.Show("Error saving image: " & ex.Message, "Error", MessageBoxButtons.OK, MessageBoxIcon.Error)
            Return originalImagePath
        End Try
    End Function

    Private Function ValidateInput() As Boolean
        ' Same validation as Add form
        Dim txtName As TextBox = CType(Me.Controls.Find("txtProductName", True).FirstOrDefault(), TextBox)
        Dim cmbCategory As ComboBox = CType(Me.Controls.Find("cmbCategory", True).FirstOrDefault(), ComboBox)
        Dim txtPrice As TextBox = CType(Me.Controls.Find("txtPrice", True).FirstOrDefault(), TextBox)
        Dim txtStock As TextBox = CType(Me.Controls.Find("txtStock", True).FirstOrDefault(), TextBox)

        If txtName.Text.Trim() = "" Then
            MessageBox.Show("Nama produk harus diisi!", "Validasi", MessageBoxButtons.OK, MessageBoxIcon.Warning)
            txtName.Focus()
            Return False
        End If

        If cmbCategory.SelectedIndex < 0 Then
            MessageBox.Show("Pilih kategori produk!", "Validasi", MessageBoxButtons.OK, MessageBoxIcon.Warning)
            cmbCategory.Focus()
            Return False
        End If

        Try
            Dim price As Decimal = Convert.ToDecimal(txtPrice.Text)
            If price <= 0 Then
                MessageBox.Show("Harga harus lebih dari 0!", "Validasi", MessageBoxButtons.OK, MessageBoxIcon.Warning)
                txtPrice.Focus()
                Return False
            End If
        Catch
            MessageBox.Show("Harga harus berupa angka!", "Validasi", MessageBoxButtons.OK, MessageBoxIcon.Warning)
            txtPrice.Focus()
            Return False
        End Try

        Try
            Dim stock As Integer = Convert.ToInt32(txtStock.Text)
            If stock < 0 Then
                MessageBox.Show("Stok tidak boleh negatif!", "Validasi", MessageBoxButtons.OK, MessageBoxIcon.Warning)
                txtStock.Focus()
                Return False
            End If
        Catch
            MessageBox.Show("Stok harus berupa angka!", "Validasi", MessageBoxButtons.OK, MessageBoxIcon.Warning)
            txtStock.Focus()
            Return False
        End Try

        Return True
    End Function
End Class